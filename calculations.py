import logging
import pandas as pd
import numpy as np
from scipy.fft import rfft, rfftfreq
# from scipy.signal import find_peaks # Unused import removed
# import ruptures as rpt # For BCPD # Unused import removed

logger = logging.getLogger(__name__)

def calculate_latest_pair_returns(latest_prices, analysis_day_data):
    """
    Calculates the log return from the last known minute close (from analysis_day_data)
    to the latest price tick/bar (from latest_prices).
    Returns a Series mapping symbol to its latest log return.
    """
    latest_returns = {}
    if not latest_prices or not analysis_day_data:
        logger.warning("Skipping latest pair return calculation due to missing input data.")
        return pd.Series(dtype=float)

    for symbol, latest_df in latest_prices.items():
        if not isinstance(latest_df, pd.DataFrame) or latest_df.empty or 'close' not in latest_df.columns:
            continue # Skip if latest price data is invalid

        if symbol not in analysis_day_data or not isinstance(analysis_day_data[symbol], pd.DataFrame) or analysis_day_data[symbol].empty:
            # logger.warning(f"Skipping {symbol} in latest pair return: Missing or empty analysis day data.") # Reduce noise
            continue # Skip if no corresponding analysis data

        try:
            latest_price = latest_df['close'].iloc[-1]
            last_minute_close = analysis_day_data[symbol]['close'].iloc[-1]
            
            if pd.isna(latest_price) or pd.isna(last_minute_close) or last_minute_close == 0:
                continue

            # Ensure prices are positive before taking log
            if latest_price > 0 and last_minute_close > 0:
                log_return = np.log(latest_price / last_minute_close)
                latest_returns[symbol] = log_return # Store log return
            else:
                continue

        except IndexError:
            # logger.warning(f"Skipping {symbol} in latest pair return: IndexError (likely empty DataFrame).") # Reduce noise
            continue
        except Exception as e:
            logger.warning(f"Error calculating latest pair return for {symbol}: {e}")
            continue

    return pd.Series(latest_returns)


# Logger definition moved to the top
# logger = logging.getLogger(__name__) # Already defined above

def calculate_pair_returns_since_sod(analysis_day_data):
    """
    Calculates the time series of cumulative log-returns for each pair from the
    start of the analysis day (first available price) to each subsequent minute.
    """
    if not isinstance(analysis_day_data, dict):
        print("Error: Input 'analysis_day_data' must be a dictionary.")
        return pd.DataFrame()

    all_sod_log_returns_ts = {} # Renamed variable
    common_index = None

    for symbol, df in analysis_day_data.items():
        if not isinstance(df, pd.DataFrame) or df.empty or 'close' not in df.columns:
            print(f"Warning: Skipping {symbol} in SOD log-return TS calc due to invalid/empty DataFrame.")
            continue

        df_sorted = df.sort_index()

        if len(df_sorted) < 1:
             print(f"Warning: Not enough data points for {symbol} to calculate SOD log-return time series.")
             continue

        sod_price = df_sorted['close'].iloc[0]

        # Check for invalid SOD price (NaN, zero, or negative)
        if pd.isna(sod_price) or sod_price <= 0:
            print(f"Warning: Invalid SOD price found for {symbol} ({sod_price}). Cannot calculate log-return time series.")
            continue

        close_prices = pd.to_numeric(df_sorted['close'], errors='coerce')

        # Handle potential issues with close_prices before division/log
        # Replace non-positive close prices with NaN to avoid errors in log calculation
        close_prices_positive = close_prices.where(close_prices > 0, np.nan)

        # Calculate log returns
        sod_log_returns_ts = np.log(close_prices_positive / sod_price) # Changed calculation

        # Check if all returns became NaN (e.g., if all close_prices were <= 0)
        if sod_log_returns_ts.isnull().all():
            print(f"Warning: All close prices for {symbol} were non-positive after SOD. Skipping log-return calculation.")
            continue

        all_sod_log_returns_ts[symbol] = sod_log_returns_ts # Store log returns

        if common_index is None:
            common_index = sod_log_returns_ts.index
        else:
            common_index = common_index.union(sod_log_returns_ts.index)

    if not all_sod_log_returns_ts or common_index is None:
         print("Error: Could not calculate any valid SOD log-return time series.")
         return pd.DataFrame()

    combined_df = pd.DataFrame(index=common_index)
    for symbol, ts in all_sod_log_returns_ts.items():
        combined_df[symbol] = ts.reindex(common_index).ffill()

    # Backfill any remaining NaNs at the beginning
    combined_df = combined_df.bfill()
    return combined_df

def calculate_basket_returns(pair_returns_sod_ts, basket_definitions):
    """
    Calculates the time series of aggregated returns for each currency basket
    using vectorized operations. Assumes input returns are log-returns if this
    function is called after the log-return refactoring.
    """
    # Note: The logic here assumes additive properties, which holds for log returns.
    # If the input `pair_returns_sod_ts` contains simple returns, the aggregation
    # method might need adjustment depending on the desired outcome.
    if not isinstance(pair_returns_sod_ts, pd.DataFrame) or pair_returns_sod_ts.empty:
        print("Warning: Invalid or empty 'pair_returns_sod_ts' DataFrame provided for basket returns calculation.")
        return pd.DataFrame()

    basket_returns_ts = pd.DataFrame(index=pair_returns_sod_ts.index)

    for basket_name, components in basket_definitions.items():
        valid_components = {symbol: sign for symbol, sign in components.items()
                         if symbol in pair_returns_sod_ts.columns}

        if not valid_components:
            print(f"Warning: Basket {basket_name} had no valid components for basket returns calculation.")
            basket_returns_ts[basket_name] = np.nan
            continue

        # Select only the relevant columns and fill NaNs
        # For log returns, filling with 0 is appropriate as log(1) = 0 (no change)
        basket_data = pair_returns_sod_ts[list(valid_components.keys())].fillna(0.0)

        # Apply signs and sum (additive for log returns)
        signs = pd.Series(list(valid_components.values()), index=list(valid_components.keys()))
        basket_returns_ts[basket_name] = basket_data.mul(signs).sum(axis=1)

    return basket_returns_ts

def calculate_basket_realized_volatility(historical_data, basket_definitions):
    """
    Calculates the realized volatility for each defined basket based on
    historical minute-by-minute log-returns.

    The calculation follows: RealizedVol = sqrt(Sum[minute_log_returns^2]).

    Args:
        historical_data (dict): A dictionary where keys are pair symbols (str)
                                and values are pandas DataFrames containing
                                historical price data (at least 'close' column
                                and a DatetimeIndex).
        basket_definitions (dict): A dictionary defining the baskets. Keys are
                                   basket names (str), values are dictionaries
                                   where keys are component pair symbols (str)
                                   and values are their signs (+1 or -1).

    Returns:
        pandas.Series: A Series where the index is the basket name and values
                       are the calculated realized volatility for each basket.
                       Returns an empty Series if calculation is not possible
                       or no valid baskets are found. Returns NaN for baskets
                       where volatility cannot be computed (e.g., zero variance).
    """
    if not historical_data:
        logger.warning("Cannot calculate pair log-returns: historical_data is empty.")
        return pd.Series(dtype=float) # Return empty Series

    # 1. Calculate historical pair log returns (minute-by-minute)
    all_pair_log_returns = {}
    common_index = None
    for symbol, df in historical_data.items():
        if isinstance(df, pd.DataFrame) and not df.empty and 'close' in df.columns and len(df) > 1:
            # Ensure prices are positive before log calculation
            safe_close = df['close'].where(df['close'] > 0)
            # Calculate log returns
            log_returns = np.log(safe_close / safe_close.shift(1)).dropna()
            if not log_returns.empty:
                all_pair_log_returns[symbol] = log_returns
                if common_index is None:
                    common_index = log_returns.index
                else:
                    # Use union and fillna later, consistent with previous logic.
                    common_index = common_index.union(log_returns.index)
        # else: # Optional: Log skipped symbols
            # logger.debug(f"Skipping {symbol} for log-return calculation: Invalid data or insufficient length.")


    if not all_pair_log_returns or common_index is None:
        logger.warning("No valid historical pair log-returns found.")
        return pd.Series(dtype=float) # Return empty Series

    # Combine into a single DataFrame, aligning indices
    pair_log_returns_df = pd.DataFrame(index=common_index)
    for symbol, log_rets in all_pair_log_returns.items():
        pair_log_returns_df[symbol] = log_rets.reindex(common_index) # Fills non-overlapping with NaN

    # Fill NaNs that might result from reindexing or initial log calculation issues
    # Filling with 0 implies no change for log returns.
    pair_log_returns_df = pair_log_returns_df.ffill(limit=5).bfill(limit=5).fillna(0.0)

    # 2. Calculate minute-by-minute basket log-returns
    if not basket_definitions:
        logger.warning("Basket definitions are empty. Cannot calculate basket log-returns.")
        # If no baskets defined, realized vol is also empty
        return pd.Series(dtype=float)

    basket_log_returns_df = pd.DataFrame(index=pair_log_returns_df.index)

    for basket_name, components in basket_definitions.items():
        # Vectorized: select valid columns and apply signs
        valid_symbols = [symbol for symbol in components if symbol in pair_log_returns_df.columns]
        if valid_symbols:
            weights = pd.Series({symbol: components[symbol] for symbol in valid_symbols})
            # Fill NaNs with 0 for log returns
            basket_data = pair_log_returns_df[valid_symbols].fillna(0.0)
            # Dot product: each row is a time point, columns are weighted by sign
            basket_log_returns_df[basket_name] = basket_data.dot(weights)
        else:
            logger.warning(f"Basket '{basket_name}' had no valid component pairs found in log-returns. Setting column to NaN.")
            basket_log_returns_df[basket_name] = np.nan # Assign NaN if no components were valid
    
    # Check if the resulting DataFrame is empty or all NaN before proceeding
    if basket_log_returns_df.empty or basket_log_returns_df.isnull().all().all():
        logger.warning("Basket log returns DataFrame is empty or all NaN. Cannot calculate realized volatility.")
        return pd.Series(dtype=float) # Return empty Series

    # 3. Calculate Realized Volatility for each basket
    basket_realized_vol = pd.Series(dtype=float)
    for basket_name in basket_log_returns_df.columns:
        log_returns = basket_log_returns_df[basket_name].dropna() # Drop NaNs before squaring
        if log_returns.empty:
            basket_realized_vol[basket_name] = np.nan
            continue

        sum_sq_returns = (log_returns ** 2).sum()

        # Handle potential non-positive sum before sqrt
        if sum_sq_returns > 0:
            realized_vol = np.sqrt(sum_sq_returns)
        elif sum_sq_returns == 0:
             realized_vol = 0.0 # Volatility is zero if returns are always zero
        else:
            # This case (negative sum of squares) shouldn't happen with real numbers
            logger.warning(f"Sum of squared log returns for basket '{basket_name}' is negative ({sum_sq_returns}). Setting volatility to NaN.")
            realized_vol = np.nan # Assign NaN if sum is negative or NaN

        basket_realized_vol[basket_name] = realized_vol

    return basket_realized_vol # Return the Series of realized volatilities


def normalize_basket_returns(basket_returns_ts, basket_realized_volatility):
    """
    Normalizes the basket cumulative log-return time series by dividing by the
    calculated basket realized volatility.

    Args:
        basket_returns_ts (pd.DataFrame): DataFrame where index is datetime and
                                          columns are basket names, containing
                                          cumulative log-returns since SOD.
        basket_realized_volatility (pd.Series): Series where index is basket name
                                               and values are the calculated
                                               realized volatility for the day.

    Returns:
        pd.DataFrame: DataFrame with normalized basket returns. Returns an empty
                      DataFrame if inputs are invalid or normalization cannot
                      be performed.
    """
    if not isinstance(basket_returns_ts, pd.DataFrame) or basket_returns_ts.empty:
        logger.warning("Invalid or empty 'basket_returns_ts' for normalization.")
        return pd.DataFrame()
    if not isinstance(basket_realized_volatility, pd.Series) or basket_realized_volatility.empty:
        logger.warning("Invalid or empty 'basket_realized_volatility' for normalization.")
        # Return empty df with same index as returns if vol is missing/invalid
        return pd.DataFrame(index=basket_returns_ts.index)

    # Replace 0 volatility with NaN *before* division to avoid errors and ensure
    # division by zero results in inf or nan, not an exception.
    volatility_no_zero = basket_realized_volatility.replace(0, np.nan)

    # Ensure we only divide by valid (non-NaN) volatilities
    valid_volatility = volatility_no_zero.dropna()

    if valid_volatility.empty:
        logger.warning("No valid (non-zero, non-NaN) basket realized volatilities available for normalization.")
        # Return empty df with same index if no valid vols exist
        return pd.DataFrame(index=basket_returns_ts.index)

    # Align columns/index - only normalize baskets present in both DataFrames/Series
    common_baskets = basket_returns_ts.columns.intersection(valid_volatility.index)

    if not common_baskets.tolist():
         logger.warning("No common baskets found between returns and valid volatilities for normalization.")
         # Return empty df with same index if no common baskets
         return pd.DataFrame(index=basket_returns_ts.index)

    # Select and align data for common baskets
    returns_to_normalize = basket_returns_ts[common_baskets]
    volatility_aligned = valid_volatility[common_baskets]

    # Perform normalization using broadcasting (divides each column by the corresponding series value)
    normalized_returns_ts = returns_to_normalize.div(volatility_aligned, axis=1)

    # Apply convergence prevention: if all current values are too small, use rolling normalization
    if len(normalized_returns_ts) > 30:
        # Check the last 10 minutes for convergence
        recent_data = normalized_returns_ts.iloc[-10:]
        recent_abs_max = recent_data.abs().max().max()

        if recent_abs_max < 1e-6:  # Convergence threshold
            logger.warning(f"Detected potential convergence in normalized returns (max recent value: {recent_abs_max}). Applying rolling normalization.")

            # Use rolling standard deviation for normalization instead of historical
            for basket in common_baskets:
                basket_returns = returns_to_normalize[basket]
                # Calculate rolling volatility (30-minute window)
                rolling_vol = basket_returns.diff().rolling(window=30, min_periods=5).std()

                # Use rolling volatility where available, fallback to historical
                combined_vol = rolling_vol.fillna(volatility_aligned[basket])
                combined_vol = combined_vol.replace(0, volatility_aligned[basket])  # Avoid division by zero

                # Re-normalize using combined volatility
                normalized_returns_ts[basket] = basket_returns / combined_vol


    # Add back columns that were present in the original returns but not in volatility
    # These columns will contain only NaNs as they couldn't be normalized.
    missing_baskets = basket_returns_ts.columns.difference(common_baskets)
    if not missing_baskets.empty:
        normalized_returns_ts[missing_baskets] = np.nan

    # Ensure original column order is preserved
    normalized_returns_ts = normalized_returns_ts.reindex(columns=basket_returns_ts.columns)

    return normalized_returns_ts


def calculate_dispersion_metric(normalized_basket_returns_ts):
    """
    Calculates the dispersion metric (standard deviation across normalized basket returns at each time point).
    """
    if not isinstance(normalized_basket_returns_ts, pd.DataFrame) or normalized_basket_returns_ts.empty:
        # print("Warning: Invalid or empty 'normalized_basket_returns_ts' for dispersion calculation.")
        return pd.Series(dtype=float)

    # Calculate standard deviation across columns (axis=1) for each row (time point)
    dispersion_ts = normalized_basket_returns_ts.std(axis=1, ddof=1) # Use sample standard deviation

    return dispersion_ts

def calculate_roc(series, period=14):
    """Calculates the Rate of Change (ROC) for a given pandas Series."""
    if not isinstance(series, pd.Series):
        # print("Warning: ROC input must be a pandas Series.")
        return pd.Series(dtype=float)
    if series.empty or len(series) < period:
        # print(f"Warning: Series too short ({len(series)}) for ROC period ({period}).")
        return pd.Series(index=series.index, dtype=float) # Return NaNs

    # ROC calculation remains the same regardless of whether input is simple or log returns,
    # but the interpretation differs.
    roc = ((series - series.shift(period)) / series.shift(period)) * 100
    # If input `series` represents log values, this ROC is on the logs.
    # If a traditional price ROC is needed, calculate it before normalization/log steps.
    return roc
    
def calculate_divergence_top9_vs_basket(top9_abs_sum, basket_abs_sum):
    """
    Calculate divergence between top 9 pairs absolute sum and basket absolute sum.
    
    Args:
        top9_abs_sum (pd.Series): Absolute sum of top 9 pairs
        basket_abs_sum (pd.Series): Absolute sum of basket returns
        
    Returns:
        pd.Series: The divergence time series
    """
    if top9_abs_sum is None or basket_abs_sum is None:
        return None
        
    if top9_abs_sum.empty or basket_abs_sum.empty:
        return None
        
    try:
        # Ensure indexes are aligned
        aligned_top9 = top9_abs_sum.reindex(basket_abs_sum.index).interpolate(method='linear')
        
        # Calculate percentage change for both series (1-minute rate of change)
        top9_roc = aligned_top9.pct_change(periods=1).fillna(0)
        basket_roc = basket_abs_sum.pct_change(periods=1).fillna(0)
        
        # Divergence is the difference in ROC
        divergence_ts = top9_roc - basket_roc
        
        return divergence_ts
        
    except Exception as e:
        print(f"Error calculating divergence: {e}")
        return None

# --- Frequency Analysis ---

def calculate_rolling_dominant_frequency(dispersion_ts, window_size=60):
    """
    Calculates the dominant frequency using FFT over a rolling window.
    """
    if not isinstance(dispersion_ts, pd.Series) or dispersion_ts.empty or len(dispersion_ts) < 2:
        return pd.Series(dtype=float)

    # Ensure index is datetime
    if not pd.api.types.is_datetime64_any_dtype(dispersion_ts.index):
         logger.warning("Dispersion time series index is not datetime. Cannot calculate rolling frequency accurately.")
         return pd.Series(dtype=float)

    # Calculate time differences in seconds for frequency calculation
    time_diffs = dispersion_ts.index.to_series().diff().dt.total_seconds().fillna(0)
    avg_sample_interval = time_diffs[1:].mean() # Average interval excluding the first NaN

    if pd.isna(avg_sample_interval) or avg_sample_interval <= 0:
        logger.warning("Could not determine a valid average sample interval for FFT.")
        return pd.Series(dtype=float)

    def get_dominant_freq(window_series):
        if len(window_series) < 2:
            return np.nan
        # Detrend the window data
        detrended_window = window_series - np.mean(window_series)
        # Apply FFT
        yf = rfft(detrended_window.values)
        xf = rfftfreq(len(detrended_window), d=avg_sample_interval) # Use average interval

        # Find the frequency with the maximum amplitude (ignoring DC component at index 0)
        if len(yf) > 1:
            max_amplitude_index = np.argmax(np.abs(yf[1:])) + 1
            dominant_freq = xf[max_amplitude_index]
            # Convert frequency (Hz) to period in minutes
            if dominant_freq != 0:
                period_seconds = 1 / dominant_freq
                period_minutes = period_seconds / 60
                return period_minutes
            else:
                return np.inf # Infinite period for zero frequency
        else:
            return np.nan

    # Apply the function over a rolling window
    dominant_freq_ts = dispersion_ts.rolling(window=window_size, min_periods=max(2, window_size // 2)).apply(get_dominant_freq, raw=False)

    return dominant_freq_ts

# --- Area Dispersion & Sentiment ---

def calculate_area_dispersion(normalized_returns_ts, area_groups):
    """Calculates dispersion within specific geographical/economic areas."""
    if not isinstance(normalized_returns_ts, pd.DataFrame) or normalized_returns_ts.empty:
        return pd.Series(dtype=float)
    if not isinstance(area_groups, dict):
        return pd.Series(dtype=float)

    area_dispersion = pd.DataFrame(index=normalized_returns_ts.index)
    for area, baskets in area_groups.items():
        valid_baskets = [b for b in baskets if b in normalized_returns_ts.columns]
        if len(valid_baskets) > 1: # Need at least 2 baskets to calculate std dev
            area_dispersion[area] = normalized_returns_ts[valid_baskets].std(axis=1, ddof=1)
        else:
            area_dispersion[area] = np.nan # Not enough data for dispersion

    return area_dispersion

def calculate_sentiment_metric(normalized_returns_ts, risk_on_baskets, risk_off_baskets):
    """Calculates a sentiment metric based on risk-on vs risk-off basket performance."""
    if not isinstance(normalized_returns_ts, pd.DataFrame) or normalized_returns_ts.empty:
        return pd.Series(dtype=float), pd.Series(dtype=float) # Return two empty series

    valid_risk_on = [b for b in risk_on_baskets if b in normalized_returns_ts.columns]
    valid_risk_off = [b for b in risk_off_baskets if b in normalized_returns_ts.columns]

    if not valid_risk_on or not valid_risk_off:
        return pd.Series(dtype=float), pd.Series(dtype=float)

    avg_risk_on = normalized_returns_ts[valid_risk_on].mean(axis=1)
    avg_risk_off = normalized_returns_ts[valid_risk_off].mean(axis=1)

    sentiment_ts = avg_risk_on - avg_risk_off

    # Example: Calculate difference between CAD and USD specifically if needed elsewhere
    cad_usd_diff_ts = pd.Series(dtype=float)
    if 'CAD' in normalized_returns_ts.columns and 'USD' in normalized_returns_ts.columns:
         cad_usd_diff_ts = normalized_returns_ts['CAD'] - normalized_returns_ts['USD']

    return sentiment_ts, cad_usd_diff_ts


def calculate_cssd(normalized_returns_df):
    """
    Calculate the Cross-Sectional Standard Deviation (CSSD) of normalized returns for each timestamp.

    Parameters
    ----------
    normalized_returns_df : pd.DataFrame
        DataFrame where rows are timestamps (DatetimeIndex) and columns are asset pairs,
        containing normalized log returns (normalized by realized volatility).

    Returns
    -------
    pd.Series
        Series indexed by timestamp, with CSSD (standard deviation across pairs) at each time.
    """
    if not isinstance(normalized_returns_df, pd.DataFrame) or normalized_returns_df.empty:
        return pd.Series(dtype=float)
    return normalized_returns_df.std(axis=1, ddof=1)


def calculate_rolling_dispersion(cssd_series):
    """
    Calculate rolling dispersion (expanding std) of CSSD values since the start of each day.

    For each day, computes the expanding standard deviation of CSSD from the first timestamp
    of the day up to each point.

    Parameters
    ----------
    cssd_series : pd.Series
        Series indexed by timestamp (DatetimeIndex), containing CSSD values.

    Returns
    -------
    pd.Series
        Series indexed by timestamp, with rolling dispersion (expanding std) for each day.
    """
    if not isinstance(cssd_series, pd.Series) or cssd_series.empty:
        return pd.Series(dtype=float)
    if not pd.api.types.is_datetime64_any_dtype(cssd_series.index):
        raise ValueError("cssd_series index must be a DatetimeIndex.")

    # Group by date and compute expanding std within each group
    return cssd_series.groupby(cssd_series.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)