import MetaTrader5 as mt5
import numpy as np
import pandas as pd
import psutil
from datetime import datetime, timedelta, time as dt_time # Import time class from datetime
import pytz
import time

from config import SERVER_TIMEZONE
import logging # Import logging

# Configure logging for this module
logger = logging.getLogger(__name__)
# Ensure basicConfig is called elsewhere (e.g., matrix24.py)
##################################################
#---MT5 & DATA---#e
def connect_mt5(retries=3, delay=5):
    """Attempts to connect to the MetaTrader 5 terminal with retry logic."""
    for attempt in range(retries):
        if mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
            print("Successfully connected to MetaTrader 5.")
            return True
        print(f"MT5 initialize() failed, error code = {mt5.last_error()}. Attempt {attempt + 1}/{retries}")
        if attempt < retries - 1:
            print(f"Retrying in {delay} seconds...")
            time.sleep(delay)
    print("Failed to connect to MetaTrader 5 after multiple attempts.")
    return False

def fetch_data(symbols, timeframe=mt5.TIMEFRAME_M1, shift=0, hours=240, start_time=None, end_time=None, data_type='bars'):
    current_usage = psutil.cpu_percent(interval=0.1)
    if current_usage > 80:
        time.sleep(0.2)
    elif current_usage > 70:
        time.sleep(0.1)

    # Determine start and end times
    if start_time is None or end_time is None:
        end_calc = datetime.now(SERVER_TIMEZONE) + timedelta(hours=3)
        if end_calc.weekday() >= 5:
            days_to_friday = (end_calc.weekday() - 4) % 7
            if days_to_friday == 0 and end_calc.weekday() == 4:
                end_calc = end_calc.replace(hour=23, minute=59, second=59, microsecond=0)
            else:
                last_friday_date = (end_calc - timedelta(days=days_to_friday)).date()
                end_calc = SERVER_TIMEZONE.localize(datetime.combine(last_friday_date, datetime.max.time())).replace(hour=23, minute=59, second=59, microsecond=0)
        start_calc = end_calc - timedelta(hours=hours)
        date_rng = pd.date_range(start_calc, end_calc, freq='min')
        bus_minutes = sum(1 for dt in date_rng if dt.weekday() < 5)
        required_minutes = hours * 60
        if bus_minutes < required_minutes:
            time_diff_estimate = timedelta(hours=(required_minutes - bus_minutes) / 60 * 1.5)
            start_calc = start_calc - time_diff_estimate
            while start_calc.weekday() >= 5:
                start_calc -= timedelta(days=1)
    else:
        start_calc = start_time + timedelta(hours=3)
        end_calc = end_time + timedelta(hours=3)

    data = {}
    for symbol in symbols:
        try:
            # Convert datetime objects to UTC timezone explicitly
            start_calc_utc = start_calc.astimezone(pytz.UTC)
            end_calc_utc = end_calc.astimezone(pytz.UTC)

            # Convert request times to UTC before converting to timestamps
            start_ts = int(start_calc_utc.timestamp())
            end_ts = int(end_calc_utc.timestamp())
            # Add retry logic for data fetching
            max_retries = 3
            retry_delay = 1.0
            for retry in range(max_retries):
                rates = mt5.copy_rates_range(symbol, timeframe, start_ts, end_ts)
                if rates is not None and len(rates) > 0:
                    break
                if retry < max_retries - 1:
                    logger.warning(f"Retry {retry + 1}/{max_retries} for {symbol}")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
            
            if rates is None or len(rates) == 0:
                logger.error(f"Failed to retrieve data for {symbol} after {max_retries} attempts")
                continue

            df = pd.DataFrame(rates)[['time', 'close']]
            if data_type == 'ticks':
                df['time'] = pd.to_datetime(df['time'], unit='s', utc=True).dt.tz_convert('Europe/Bucharest')
            else:
                df['time'] = pd.to_datetime(df['time'], unit='s').dt.tz_localize('Europe/Bucharest')
            df.set_index('time', inplace=True)
            df.sort_index(inplace=True)
            df = df[df.index.dayofweek < 5]
            df = df[~((df.index.dayofweek == 5) | (df.index.dayofweek == 6))]
            data[symbol] = df
        except Exception as e:
            print(f"Exception fetching data for {symbol}: {e}")
            continue
    return data

##################################################

# NEW function to get historical data
def get_historical_data(symbols, lookback_days):
    """
    Fetches historical data ending just before the start of the current analysis day,
    leveraging the existing fetch_data function for time handling consistency.
    """
    if not fetch_data: # Check if fetch_data is available
        print("Error: fetch_data function not available.")
        return None

    now = datetime.now(SERVER_TIMEZONE)
    today_weekday = now.weekday() # Monday is 0 and Sunday is 6

    # Determine the target analysis day (today or last Friday)
    if today_weekday == 5: # Saturday
        analysis_date = (now - timedelta(days=1)).date()
    elif today_weekday == 6: # Sunday
        analysis_date = (now - timedelta(days=2)).date()
    else: # Weekday
        analysis_date = now.date()

    # Define the end time for historical data (exclusive end - start of analysis day 00:00:00)
    # This time needs to be in the SERVER_TIMEZONE before passing to fetch_data
    end_historical_dt_server = SERVER_TIMEZONE.localize(datetime.combine(analysis_date, dt_time.min))

    # Define the start time for the historical lookback period
    start_historical_dt_server = end_historical_dt_server - timedelta(days=lookback_days)

    print(f"Fetching historical data from {start_historical_dt_server} up to (but not including) {end_historical_dt_server}...")

    # Fetch historical data using the existing fetch_data function
    # Note: fetch_data will add 3 hours to both start and end times to ensure full data coverage
    # This is consistent with how current day data is fetched
    historical_data = fetch_data(
        symbols=symbols,
        timeframe=mt5.TIMEFRAME_M1,  # Assuming M1 is desired
        start_time=start_historical_dt_server,
        end_time=end_historical_dt_server  # Pass exact start of analysis day as end
    )

    if not historical_data:
        print("Warning: fetch_data returned no historical data.")
        return {} # Return empty dict on failure

    # Basic validation - ensure dict structure
    if not isinstance(historical_data, dict):
         print(f"Warning: fetch_data did not return a dictionary for historical data. Type: {type(historical_data)}")
         return {}

    # Ensure all symbols are present as keys, even if data is empty, and index is correct tz
    validated_data = {}
    for symbol in symbols:
        df = historical_data.get(symbol)
        if df is None or not isinstance(df, pd.DataFrame):
             print(f"Warning: No valid DataFrame found for historical data of {symbol}. Creating empty.")
             validated_data[symbol] = pd.DataFrame(columns=['close']) # Ensure 'close' column exists
        else:
             # Ensure index is timezone-aware (fetch_data should handle this, but double-check)
             if not df.empty: # Only process non-empty frames
                 if df.index.tz is None:
                      # This case might indicate an issue in fetch_data's timezone handling
                      print(f"Warning: Historical data for {symbol} has naive index. Localizing to {SERVER_TIMEZONE.zone}.")
                      try:
                          df.index = df.index.tz_localize(SERVER_TIMEZONE)
                      except Exception as tz_err:
                           print(f"Error localizing index for {symbol}: {tz_err}")
                           # Handle error case, maybe return empty df?
                           validated_data[symbol] = pd.DataFrame(columns=['close'])
                           continue
                 elif df.index.tz.zone != SERVER_TIMEZONE.zone:
                      print(f"Warning: Historical data for {symbol} has incorrect timezone ({df.index.tz.zone}). Converting to {SERVER_TIMEZONE.zone}.")
                      try:
                          df.index = df.index.tz_convert(SERVER_TIMEZONE)
                      except Exception as tz_err:
                           print(f"Error converting index timezone for {symbol}: {tz_err}")
                           validated_data[symbol] = pd.DataFrame(columns=['close'])
                           continue

                 # Final check: Ensure no data *on or after* the intended end time leaks through
                 df = df[df.index < end_historical_dt_server]

             validated_data[symbol] = df

    return validated_data


def fetch_latest_prices(symbols):
    """
    Fetches the latest available price data (e.g., last 2 M1 bars) for a list of symbols.
    Getting the last 2 bars allows calculating the change for the most recent interval.

    Args:
        symbols (list): A list of symbol strings.

    Returns:
        dict: A dictionary where keys are symbols and values are pandas DataFrames
              containing the latest price bar (time, open, high, low, close, volume).
              Returns an empty dict if connection fails or no data is retrieved.
    """
    logger.info(f"Fetching latest prices for {len(symbols)} symbols...")
    if not connect_mt5():
        logger.error("Failed to connect to MT5 for fetching latest prices.")
        return {}

    latest_prices_data = {}
    for symbol in symbols:
        try:
            end_time = datetime.now(SERVER_TIMEZONE) + timedelta(hours=3)
            start_time = end_time - timedelta(minutes=2)
            rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, start_time, end_time)
            if rates is None or len(rates) == 0:
                logger.error(f"Rates returned for {symbol} by copy_rates_range is None or has length {len(rates) if rates is not None else 'None'}.")
                continue

            df = pd.DataFrame(rates)[['time', 'close']]
            df['time'] = pd.to_datetime(df['time'], unit='s').dt.tz_localize('Europe/Bucharest')
            df.set_index('time', inplace=True)
            #df.sort_index(inplace=True)
            df = df[df.index.dayofweek < 5]
            df = df[~((df.index.dayofweek == 5) | (df.index.dayofweek == 6))]
            latest_prices_data[symbol] = df

            '''
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s', utc=True)
            df['time'] = df['time'].dt.tz_convert(SERVER_TIMEZONE)
            df.set_index('time', inplace=True)
            latest_prices_data[symbol] = df
            '''
            #logger.info(f"Latest prices for {symbol} fetched successfully.")
        except Exception as e:
            logger.error(f"Error fetching latest rates for {symbol}: {e}", exc_info=True)
            continue

    logger.info(f"Finished fetching latest prices. Retrieved data for {len(latest_prices_data)} symbols.")
    return latest_prices_data

##################################################
#---DATA HANDLING---#
def calculate_returns_usd(data):
    returns = {}
    for symbol, df in data.items():
        if df.empty:
            continue
        symbol_info = mt5.symbol_info(symbol)
        multiplier = (symbol_info.trade_tick_value / symbol_info.trade_tick_size) if symbol_info else 1
        df['usd_return'] =  np.multiply(np.diff(df['close'], prepend=np.nan), multiplier)
        returns[symbol] = df['usd_return']
    return pd.DataFrame(returns)

def calculate_returns(data, copy=True):
    # Input validation
    if not isinstance(data, dict):
        raise ValueError("data must be a dictionary of DataFrames")
    
    # Create output container
    returns = {}
    
    # Process each symbol
    for symbol, df in data.items():
        # Skip empty DataFrames
        if df.empty:
            continue
            
        # Validate DataFrame and columns
        if not isinstance(df, pd.DataFrame):
            raise ValueError(f"Value for {symbol} is not a DataFrame")
        if 'close' not in df.columns:
            raise KeyError(f"DataFrame for {symbol} is missing 'close' column")
            
        # Calculate returns
        if copy:
            # Create a copy to avoid modifying the original
            df_copy = df.copy()
            df_copy['returns'] = df_copy['close'].pct_change()
            returns[symbol] = df_copy['returns']
        else:
            # Modify in place
            df['returns'] = df['close'].pct_change()
            returns[symbol] = df['returns']
            
    return pd.DataFrame(returns)

def calculate_log_returns(data):
    logreturns = {}
    for symbol, df in data.items():
        if df.empty:
            continue
        log_returns = np.diff(np.log(df['close']), prepend=np.nan)
        logreturns[symbol] = log_returns
    return pd.DataFrame(logreturns)

def get_relevant_data(symbols, lookback_days):
    """
    Fetches historical data for normalization and current day data using func_mt5.fetch_data.
    Handles weekends by fetching data for the previous Friday.
    """
    if not fetch_data:
        print("Error: fetch_data function not available.")
        return None

    now = datetime.now(SERVER_TIMEZONE)
    today_weekday = now.weekday() # Monday is 0 and Sunday is 6

    # Determine the target day for analysis (today or last Friday)
    if today_weekday == 5: # Saturday
        print("Detected Saturday, analyzing data for previous Friday.")
        analysis_date = (now - timedelta(days=1)).date()
    elif today_weekday == 6: # Sunday
        print("Detected Sunday, analyzing data for previous Friday.")
        analysis_date = (now - timedelta(days=2)).date()
    else: # Weekday
        analysis_date = now.date()

    # Define the start and end of the target analysis day
    start_of_analysis_day = SERVER_TIMEZONE.localize(datetime.combine(analysis_date, dt_time.min)).replace(hour=0, microsecond=0, minute=0, second=0)
    end_of_analysis_day = SERVER_TIMEZONE.localize(datetime.combine(analysis_date, dt_time.max)).replace(microsecond=0)

    # Define the start for the historical lookback period (relative to the analysis day)
    historical_start_time = start_of_analysis_day - timedelta(days=lookback_days + 1) # Add buffer

    print(f"Determined analysis day: {analysis_date}")
    print(f"Fetching data from {historical_start_time} up to {end_of_analysis_day}...")

    # Fetch all data in one go
    # Note: fetch_data will add 3 hours to both start and end times to ensure full data coverage
    # This ensures consistent behavior with get_historical_data
    all_data = fetch_data(
        symbols=symbols,
        timeframe=mt5.TIMEFRAME_M1,
        start_time=historical_start_time,
        end_time=end_of_analysis_day
    )

    if not all_data:
        print("Error: Failed to fetch data using func_mt5.fetch_data.")
        return None

    # Separate historical and "analysis day" data
    historical_data = {}
    analysis_day_data = {}

    for symbol, df in all_data.items():
        if df.empty:
            print(f"Warning: No data returned for {symbol} in the requested range.")
            historical_data[symbol] = pd.DataFrame(columns=['close'])
            analysis_day_data[symbol] = pd.DataFrame(columns=['close'])
            continue

        # Ensure the index is timezone-aware
        if df.index.tz is None:
             df.index = df.index.tz_localize(SERVER_TIMEZONE)
        elif df.index.tz != SERVER_TIMEZONE:
             df.index = df.index.tz_convert(SERVER_TIMEZONE)

        # Filter data for the historical period (up to the start of the analysis day)
        hist_mask = df.index < start_of_analysis_day
        historical_data[symbol] = df[hist_mask].copy()

        # Filter data for the analysis day (>= start and <= end)
        curr_mask = (df.index >= start_of_analysis_day) & (df.index <= end_of_analysis_day)
        analysis_day_data[symbol] = df[curr_mask].copy()

        if historical_data[symbol].empty:
            print(f"Warning: No historical data found for {symbol} before {start_of_analysis_day}")
        if analysis_day_data[symbol].empty:
            print(f"Warning: No data found for {symbol} on analysis day {analysis_date}")

    return {
        'historical': historical_data,
        'analysis_day': analysis_day_data
    }

def get_usd_multiplier(symbol):
    """
    Gets the multiplier to convert price change to USD change for a symbol.
    """
    try:
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Warning: Could not get symbol info for {symbol}. Using multiplier 1.")
            return 1.0
        tick_value = symbol_info.trade_tick_value
        tick_size = symbol_info.trade_tick_size
        if tick_size is None or tick_size == 0 or tick_value is None:
             print(f"Warning: Invalid tick_size ({tick_size}) or tick_value ({tick_value}) for {symbol}. Using multiplier 1.")
             return 1.0
        return tick_value / tick_size
    except Exception as e:
        print(f"Error getting symbol info for {symbol}: {e}. Using multiplier 1.")
        return 1.0

def _get_mql_pip_value(symbol):
    """
    Calculate the MQL4-style pip value for a given symbol.
    Returns the monetary value of 1 pip movement per standard lot.
    """
    try:
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Warning: Could not get symbol info for {symbol}. Using default pip value 10.")
            return 10.0  # Default value, assuming standard pip movement
            
        # Get the basic information
        tick_size = symbol_info.trade_tick_size
        tick_value = symbol_info.trade_tick_value
        point = symbol_info.point
        
        if tick_size == 0 or point == 0:
            print(f"Warning: Invalid tick_size ({tick_size}) or point ({point}) for {symbol}. Using default pip value 10.")
            return 10.0
        
        # Determine standard pip size based on symbol
        pip_size = 0.0001
        if symbol.endswith("JPY"):
            pip_size = 0.01
            
        # Calculate pip value
        pip_value = (pip_size / point) * tick_value
        
        if pip_value == 0:
            print(f"Warning: Calculated pip value is 0 for {symbol}. Using default of 10.")
            return 10.0
            
        return pip_value
    
    except Exception as e:
        print(f"Error calculating pip value for {symbol}: {e}. Using default of 10.")
        return 10.0