import plotly.io as pio
import numpy as np
import pandas as pd
from datetime import datetime, time
import time as timer # Import time module for timing
import plotly.colors as pc
import plotly.graph_objects as go  # Import plotly colors

from config import ALL_SYMBOLS, SERVER_TIMEZONE, AREA_GROUP_COLORS
from func_phases import StrengthRanker, ColorGradient, StrengthChartBuilder

def create_rolling_dispersion_chart(normalized_returns_ts, color_map=None):
    start_time = timer.time() # Record start time
    """
    Create a Plotly line chart showing rolling CSSD dispersion for all currency pairs, using normalized pair returns.

    Parameters
    ----------
    normalized_returns_ts : pd.DataFrame
        DataFrame with normalized pair returns (columns: pairs, index: timestamps).
    color_map : dict or None
        Optional dict mapping pair names to colors.

    Returns
    -------
    dict
        Dictionary containing:
        - 'figure': plotly.graph_objs.Figure - The dispersion chart
        - 'near_peak_weights_str': str - Comma-separated string of pairs near their peak rolling dispersion and weights
    """

    # Compute rolling dispersion for each pair (expanding std per day) using normalized returns
    rolling_dispersion_df = pd.DataFrame(index=normalized_returns_ts.index)
    for pair in normalized_returns_ts.columns:
        series = normalized_returns_ts[pair]
        # Group by date, expanding std per day
        rolling = series.groupby(series.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)
        rolling_dispersion_df[pair] = rolling

    # Forward fill the calculated rolling dispersion to extend lines
    rolling_dispersion_df = rolling_dispersion_df.ffill()

    # Color mapping: use basket_colors by base currency (to match "Top 9 Strongest Pairs" chart)
    if color_map is None:
        basket_colors = {
            "USD": "lime", "EUR": "dodgerblue", "GBP": "red", "AUD": "orange",
            "NZD": "aqua", "CAD": "fuchsia", "CHF": "silver", "JPY": "yellow"
        }
        def get_pair_color(pair):
            base = pair[:3]
            return basket_colors.get(base, "#888")
        color_map = {pair: get_pair_color(pair) for pair in normalized_returns_ts.columns}

    # Sort pairs by their final rolling dispersion value for legend order
    if not rolling_dispersion_df.empty:
        final_values = rolling_dispersion_df.iloc[-1].dropna()
        sorted_pairs_by_final_value = final_values.sort_values(ascending=False).index.tolist()
    else:
        sorted_pairs_by_final_value = rolling_dispersion_df.columns.tolist() # Fallback if empty

    fig = go.Figure()
    for pair in sorted_pairs_by_final_value: # Iterate using the new sorted list
        # Reindex y to the full normalized_returns_ts index to ensure lines extend to the latest timestamp
        y = rolling_dispersion_df[pair] # Use the forward-filled data directly
        # Find peak timestamp for this pair
        peak_idx = y.idxmax() if not y.isnull().all() else None
        peak_val = y.max() if not y.isnull().all() else np.nan # Use NaN if no peak

        # Assign a single color for the line based on the last normalized return value
        base = pair[:3]
        quote = pair[3:]
        basket_colors = {
            "USD": "lime", "EUR": "dodgerblue", "GBP": "red", "AUD": "orange",
            "NZD": "aqua", "CAD": "fuchsia", "CHF": "silver", "JPY": "yellow"
        }
        base_color = basket_colors.get(base, "#888")
        quote_color = basket_colors.get(quote, "#888")
        last_val = normalized_returns_ts[pair].dropna()
        last_val = last_val.iloc[-1] if not last_val.empty else 0
        color = base_color if last_val > 0 else quote_color

        # Prepare customdata for hovertemplate
        # Check if the current y value is close to the peak value for this pair
        is_peak = np.isclose(y, peak_val, atol=1e-8, equal_nan=True) # Compare y values to the single peak_val
        peak_marker = np.where(is_peak, "*", "") # Add '*' if it's the peak

        customdata_stack = [
            np.full_like(y, pair, dtype=object), # Pair name
            np.full_like(y, color, dtype=object), # Color
            peak_marker # Peak indicator '*' or ''
        ]
        customdata_array = np.stack(customdata_stack, axis=-1) if len(y) else None

        fig.add_trace(go.Scatter(
            x=y.index,
            y=y,
            mode="lines",
            name=f"{pair} {'★' if not y.empty and np.isclose(y.iloc[-1], peak_val, atol=1e-8, equal_nan=True) else ''}",
            line=dict(color=color, width=2),
            # Removed legendgroup and legendgrouptitle_text to sort by trace order (peak dispersion)
            hovertemplate=(
                "<span style='color:%{customdata[1]};'>●</span> " # Color indicator
                "%{customdata[0]} " # Pair
                "%{y:.4f}" # Dispersion value
                "%{customdata[2]}" # Peak marker (*)
                "<extra></extra>" # Hide trace info
            ),
            customdata=customdata_array,
        ))
        # Add back the separate peak marker trace
        if peak_idx is not None and not pd.isnull(peak_val):
             # Use the same color as the line for the marker
            fig.add_trace(go.Scatter(
                x=[peak_idx],
                y=[peak_val],
                mode="markers",
                marker=dict(color=color, size=10, symbol="star"),
                name=f"{pair} Peak", # Keep name for potential debugging, but hide from legend
                showlegend=False,
                hovertemplate=(
                    "<b>Peak:</b> %{customdata[0]}<br>" # Pair name from customdata
                    "Value: %{y:.4f}" # Peak value
                    "<extra></extra>" # Hide trace info
                ),
                customdata=np.array([[pair]]), # Pass pair name for hover
            ))

    # Calculate x-axis range based on the full normalized_returns_ts index
    idx = normalized_returns_ts.index
    x_range = None
    if len(idx) > 0:
        first_ts = idx[0]
        last_ts = idx[-1]
        tz = getattr(first_ts, 'tz', None)
        if tz is None:
            from config import SERVER_TIMEZONE
            tz = SERVER_TIMEZONE
        from datetime import timedelta, datetime as dt
        if hasattr(first_ts, "date"):
            start_of_day = tz.localize(dt.combine(first_ts.date(), dt.min.time()), is_dst=None)
        else:
            start_of_day = idx.min() # Fallback if no date attribute
        x_range = [start_of_day, last_ts + timedelta(minutes=1)] # Pad by 1 minute

    fig.update_layout(
        title="Rolling Dispersion of Normalized Pair Returns CSSD", # Updated title
        xaxis_title="Time",
        yaxis_title="Rolling CSSD Dispersion", # Updated y-axis title
        template="plotly_dark",
        legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01),
        hovermode="x unified",
        height=800,
        margin=dict(l=40, r=20, t=60, b=40), # Reduced right margin
        font=dict(size=14),
        xaxis_domain=[0, 1], # Ensure x-axis covers full width
        autosize=True,
    )
    if x_range:
        fig.update_xaxes(range=x_range)

    end_time = timer.time() # Record end time
    duration = end_time - start_time
    print(f"Time to prepare and plot rolling dispersion chart: {duration:.4f} seconds")

    # Calculate near-peak pairs and their weights
    near_peak_pairs = {}
    star_pairs = {}  # New dictionary for pairs at exact peak
    for pair in sorted_pairs_by_final_value:
        y = rolling_dispersion_df[pair]
        peak_val = y.max() if not y.isnull().all() else np.nan
        latest_val = y.iloc[-1] if not y.empty else np.nan
        
        # Check if current value is within 5% of peak
        is_near_peak = (peak_val != 0 and not pd.isna(latest_val) and not pd.isna(peak_val)
                       and latest_val >= peak_val * 0.95)
        
        # Check if current value equals peak value
        is_at_peak = (not y.empty and np.isclose(latest_val, peak_val, atol=1e-8, equal_nan=True))
        
        if is_near_peak:
            # Get latest normalized return
            last_norm_return = normalized_returns_ts[pair].dropna()
            if not last_norm_return.empty:
                last_norm_return = last_norm_return.iloc[-1]
                near_peak_pairs[pair] = last_norm_return
                
        if is_at_peak:  # Add to star_pairs if at exact peak
            last_norm_return = normalized_returns_ts[pair].dropna()
            if not last_norm_return.empty:
                last_norm_return = last_norm_return.iloc[-1]
                star_pairs[pair] = last_norm_return

    # Calculate weights string
    weights_str = ""
    if near_peak_pairs:
        # Calculate sum of absolute values for normalization
        abs_sum = sum(abs(val) for val in near_peak_pairs.values())
        if abs_sum > 0:  # Avoid division by zero
            normalized_weights = {pair: val / abs_sum for pair, val in near_peak_pairs.items()}
            # Sort by absolute weight values while preserving signs
            sorted_weights = dict(sorted(normalized_weights.items(), key=lambda item: abs(item[1]), reverse=True))
            weights_str = ",".join([f"{pair}:{weight:.4f}" for pair, weight in sorted_weights.items()])
            
    # Calculate star pairs weights string
    star_weights_str = ""
    if star_pairs:
        # Calculate sum of absolute values for normalization
        abs_sum = sum(abs(val) for val in star_pairs.values())
        if abs_sum > 0:  # Avoid division by zero
            normalized_weights = {pair: val / abs_sum for pair, val in star_pairs.items()}
            # Sort by absolute weight values while preserving signs
            sorted_weights = dict(sorted(normalized_weights.items(), key=lambda item: abs(item[1]), reverse=True))
            star_weights_str = ",".join([f"{pair}:{weight:.4f}" for pair, weight in sorted_weights.items()])

    return {
        'figure': fig,
        'near_peak_weights_str': weights_str,
        'star_weights_str': star_weights_str
    }

def generate_base_market_phase_figures(market_data):
    """
    Generates Plotly base figures using pre-fetched/pre-calculated market data.
    Excludes latest tick data that is handled by Patch updates.
    """
    import pandas as pd
    import plotly.graph_objects as go

    if market_data is None:
        print("Error: No market data provided to generate_base_market_phase_figures.")
        return {}

    (
        normalized_returns_ts,
        dispersion_ts,
        hmm_states,
        historical_daily_dispersion_highs,
        pair_returns_ts,
        cusum_pos,
        cusum_neg,
        cusum_threshold,
        cusum_changepoints,
        bcpd_changepoints,
        dominant_freq_ts,
        historical_pair_std_devs,
        dispersion_roc_df
    ) = (
        market_data.get('normalized_returns_ts', pd.DataFrame()),
        market_data.get('dispersion_ts', pd.Series(dtype=float)),
        market_data.get('hmm_states', None),
        market_data.get('historical_daily_dispersion_highs', None),
        market_data.get('pair_returns_ts', pd.DataFrame()),
        market_data.get('cusum_pos', pd.Series(dtype=float)),
        market_data.get('cusum_neg', pd.Series(dtype=float)),
        market_data.get('cusum_threshold', None),
        market_data.get('cusum_changepoints', []),
        market_data.get('bcpd_changepoints', []),
        market_data.get('dominant_freq_ts', pd.Series(dtype=float)),
        market_data.get('historical_pair_std_devs', pd.Series(dtype=float)),
        market_data.get('dispersion_roc_df', pd.DataFrame(columns=['dispersion', 'disp_roc_5', 'disp_roc_14', 'disp_roc_50']))
    )

    area_dispersion_ts, sentiment_ts, cad_usd_diff_ts = pd.Series(dtype=float), pd.Series(dtype=float), pd.Series(dtype=float)

    # Calculate matrix_QP normalized returns for area dispersion and sentiment calculations
    matrix_qp_normalized_for_area = pd.DataFrame()
    if 'basket_returns_ts' in market_data and isinstance(market_data['basket_returns_ts'], pd.DataFrame):
        basket_returns_data = market_data['basket_returns_ts']

        if not basket_returns_data.empty:
            # Calculate matrix_QP realized volatility for baskets
            minute_log_returns_baskets = basket_returns_data.diff().fillna(0.0)

            realized_vol_baskets = pd.Series(dtype=float)
            for basket in minute_log_returns_baskets.columns:
                log_returns = minute_log_returns_baskets[basket].dropna()

                if log_returns.empty:
                    realized_vol_baskets[basket] = np.nan
                    continue

                # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
                sum_sq_returns = (log_returns ** 2).sum()

                if sum_sq_returns > 0:
                    realized_vol_baskets[basket] = np.sqrt(sum_sq_returns)
                elif sum_sq_returns == 0:
                    realized_vol_baskets[basket] = 0.0
                else:
                    realized_vol_baskets[basket] = np.nan

            # Matrix_QP normalization
            min_volatility_threshold = 1e-8
            volatility_no_zero = realized_vol_baskets.replace(0, np.nan)
            volatility_filtered = volatility_no_zero.where(volatility_no_zero >= min_volatility_threshold, np.nan)
            valid_volatility_baskets = volatility_filtered.dropna()

            if not valid_volatility_baskets.empty:
                common_baskets = basket_returns_data.columns.intersection(valid_volatility_baskets.index)

                if common_baskets.tolist():
                    returns_to_normalize = basket_returns_data[common_baskets]
                    volatility_aligned = valid_volatility_baskets[common_baskets]
                    normalized_common = returns_to_normalize.div(volatility_aligned, axis=1)

                    matrix_qp_normalized_for_area = pd.DataFrame(index=basket_returns_data.index)
                    for basket in common_baskets:
                        matrix_qp_normalized_for_area[basket] = normalized_common[basket]

                    missing_baskets = basket_returns_data.columns.difference(common_baskets)
                    for missing_basket in missing_baskets:
                        matrix_qp_normalized_for_area[missing_basket] = np.nan

                    matrix_qp_normalized_for_area = matrix_qp_normalized_for_area.reindex(columns=basket_returns_data.columns)

    # Use matrix_QP normalized returns if available, otherwise fallback to historical normalization
    normalized_for_calculations = matrix_qp_normalized_for_area if not matrix_qp_normalized_for_area.empty else normalized_returns_ts

    if not normalized_for_calculations.empty and not dispersion_ts.empty:
        from calculations import calculate_area_dispersion, calculate_sentiment_metric
        from config import AREA_GROUPS, RISK_ON_BASKETS, RISK_OFF_BASKETS
        area_dispersion_ts = calculate_area_dispersion(normalized_for_calculations, AREA_GROUPS)
        sentiment_ts, cad_usd_diff_ts = calculate_sentiment_metric(
            normalized_for_calculations,
            RISK_ON_BASKETS,
            RISK_OFF_BASKETS
        )

    base_height = 300
    fig_roc = go.Figure()
    fig_roc.update_layout(template="plotly_dark")
    required_roc_cols = ['disp_roc_5', 'disp_roc_14', 'disp_roc_50']

    if isinstance(dispersion_roc_df, pd.DataFrame) and not dispersion_roc_df.empty and all(col in dispersion_roc_df.columns for col in required_roc_cols):
        try:
            roc5 = dispersion_roc_df['disp_roc_5']
            roc14 = dispersion_roc_df['disp_roc_14']
            roc50 = dispersion_roc_df['disp_roc_50']

            roc50_rises = roc50.diff() > 0
            consecutive_rises = pd.Series(0, index=roc50.index)
            rise_count = 0
            for i in range(len(roc50_rises)):
                if roc50_rises.iloc[i]:
                    rise_count += 1
                else:
                    rise_count = 0
                consecutive_rises.iloc[i] = rise_count

            yellow_condition = ((roc5 > 0) & (roc14 > 0) & (roc50 > 0) & (roc5.diff() > 0) & (roc14.diff() > 0) & (roc50.diff() > 0)).fillna(False)
            aqua_condition = ((roc50 < 0) & (roc5 > 0) & (roc14 > 0) & (consecutive_rises >= 5)).fillna(False)
            red_condition = ((roc50 < 0) & ~aqua_condition).fillna(False)
            pink_condition = ((roc5 < 0) & (roc14 < 0) & (roc50 > 0) & ~yellow_condition).fillna(False)

            def find_highlight_periods(condition):
                periods = []
                start_index = None
                for i, value in enumerate(condition):
                    if value and start_index is None:
                        start_index = i
                    elif not value and start_index is not None:
                        if start_index < i:
                            periods.append((dispersion_roc_df.index[start_index], dispersion_roc_df.index[i]))
                        start_index = None
                if start_index is not None and start_index < len(dispersion_roc_df.index):
                    periods.append((dispersion_roc_df.index[start_index], dispersion_roc_df.index[-1] + pd.Timedelta(minutes=1)))
                return periods

            yellow_periods = find_highlight_periods(yellow_condition)
            red_periods = find_highlight_periods(red_condition)
            pink_periods = find_highlight_periods(pink_condition)
            aqua_periods = find_highlight_periods(aqua_condition)

            def merge_periods(periods):
                if not periods:
                    return []
                merged = []
                periods.sort(key=lambda x: x[0])
                current_start, current_end = periods[0]
                for next_start, next_end in periods[1:]:
                    if next_start <= current_end + pd.Timedelta(minutes=5):
                        current_end = max(current_end, next_end)
                    else:
                        merged.append((current_start, current_end))
                        current_start, current_end = next_start, next_end
                merged.append((current_start, current_end))
                return merged

            merged_yellow_periods = merge_periods(yellow_periods)
            merged_red_periods = merge_periods(red_periods)
            merged_pink_periods = merge_periods(pink_periods)
            merged_aqua_periods = merge_periods(aqua_periods)

        except Exception as e_highlight:
            print(f"Error calculating ROC highlight conditions: {e_highlight}")

        try:
            CLIP_LOWER = -50
            CLIP_UPPER = 100
            fig_roc.add_trace(go.Scatter(x=dispersion_roc_df.index, y=dispersion_roc_df['disp_roc_5'].clip(lower=CLIP_LOWER, upper=CLIP_UPPER), name='ROC(5)', line=dict(color='blue'), hovertemplate='ROC(5): %{customdata:.2f}%<extra></extra>', customdata=dispersion_roc_df['disp_roc_5']))
            fig_roc.add_trace(go.Scatter(x=dispersion_roc_df.index, y=dispersion_roc_df['disp_roc_14'].clip(lower=CLIP_LOWER, upper=CLIP_UPPER), name='ROC(14)', line=dict(color='orange'), hovertemplate='ROC(14): %{customdata:.2f}%<extra></extra>', customdata=dispersion_roc_df['disp_roc_14']))
            fig_roc.add_trace(go.Scatter(x=dispersion_roc_df.index, y=dispersion_roc_df['disp_roc_50'].clip(lower=CLIP_LOWER, upper=CLIP_UPPER), name='ROC(50)', line=dict(color='green'), hovertemplate='ROC(50): %{customdata:.2f}%<extra></extra>', customdata=dispersion_roc_df['disp_roc_50']))

            shapes = []
            background_configs = [(merged_red_periods, "red", 0.1), (merged_pink_periods, "pink", 0.15), (merged_aqua_periods, "aqua", 0.2), (merged_yellow_periods, "yellow", 0.2)]
            for periods, color, opacity in background_configs:
                for start, end in periods:
                    shapes.append(go.layout.Shape(type="rect", x0=start, y0=0, x1=end, y1=1, line=dict(width=0), fillcolor=color, opacity=opacity, layer="below", yref="paper"))
            if shapes:
                fig_roc.update_layout(shapes=shapes)

            legend_text = ("<b>ROC Indicators:</b><br>" + "<span style='color:blue;'>ROC(5)</span>  " + "<span style='color:orange;'>ROC(14)</span>  " + "<span style='color:green;'>ROC(50)</span><br>" + "<span style='color:yellow;'>⬒</span> All Rising & Positive<br>" + "<span style='color:red;'>⬒</span> ROC(50) < 0<br>" + "<span style='color:pink;'>⬒</span> ROC(5,14) < 0, ROC(50) > 0<br>" + "<span style='color:aqua;'>⬒</span> ROC(50) < 0 but 5↑, ROC(5,14) > 0")
            fig_roc.add_annotation(text=legend_text, align='left', showarrow=False, xref='paper', yref='paper', x=0.01, y=0.99, bgcolor='rgba(0,0,0,0.7)', bordercolor='rgba(255,255,255,0.5)', borderwidth=1, font=dict(size=10, family="monospace"))

            start_time_roc = dispersion_roc_df.index.min()
            end_time_roc = dispersion_roc_df.index.max() + pd.Timedelta(minutes=1)
            fig_roc.update_layout(
                title_text=f"Dispersion ROC (5, 14, 50) [Viz Clipped: {CLIP_LOWER}% / {CLIP_UPPER}%]",
                xaxis=dict(
                    title=dict(text="Date"),
                    range=[start_time_roc, end_time_roc],
                    showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)',
                    mirror=True, ticks='outside', showline=True, linecolor='rgba(255,255,255,0.3)', linewidth=1
                ),
                yaxis=dict(
                    title=dict(text="ROC (%)"),
                    showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)',
                    mirror=True, ticks='outside', showline=True, linecolor='rgba(255,255,255,0.3)', linewidth=1
                ),
                hovermode="x unified",
                template="plotly_dark",
                showlegend=False,
                margin=dict(l=50, r=20, t=30, b=20),
                height=base_height
            )
            fig_roc.add_hline(y=0, line_dash="dash", line_color="grey", line_width=1)
        except Exception as roc_ex:
            print(f"Market Phase: Error generating ROC plot: {roc_ex}")
            fig_roc = go.Figure(layout=go.Layout(template="plotly_dark", title_text="Error Generating ROC Plot"))
    else:
        print("Market Phase: Skipping Dispersion ROC plot generation due to missing or invalid 'dispersion_roc_df'.")
        fig_roc = go.Figure(layout=go.Layout(template="plotly_dark", title_text="Dispersion ROC (Data Unavailable)"))

    roc_background_colors = pd.Series('none', index=dispersion_roc_df.index)
    if 'yellow_condition' in locals() and 'pink_condition' in locals():
        roc_background_colors[yellow_condition] = 'yellow'
        roc_background_colors[pink_condition] = 'pink'

    plotly_figs = create_plotly_metrics(
        normalized_returns_ts=normalized_returns_ts,
        dispersion_ts=dispersion_ts,
        sentiment_ts=sentiment_ts,
        area_dispersion_ts=area_dispersion_ts,
        hmm_states=hmm_states,
        cad_usd_diff_ts=cad_usd_diff_ts,
        cusum_pos=cusum_pos,
        cusum_neg=cusum_neg,
        cusum_threshold=cusum_threshold,
        cusum_changepoints=cusum_changepoints,
        bcpd_changepoints=bcpd_changepoints,
        dominant_freq_ts=dominant_freq_ts,
        historical_daily_dispersion_highs=historical_daily_dispersion_highs,
        pair_returns_ts=pair_returns_ts,
        historical_pair_std_devs=historical_pair_std_devs,
        dispersion_roc_ts=dispersion_roc_df['disp_roc_5'] if not dispersion_roc_df.empty else None,
        roc_background_colors=roc_background_colors,
        basket_returns_ts=market_data.get('basket_returns_ts', pd.DataFrame())
    )

    plotly_figs['dispersion_roc'] = fig_roc

    from plotting import create_rolling_dispersion_chart
    if 'pair_returns_ts' in market_data and isinstance(market_data['pair_returns_ts'], pd.DataFrame):
        # Calculate normalized pair returns exactly like matrix_QP
        pair_returns_data = market_data['pair_returns_ts']  # Cumulative log returns since SOD

        # Calculate realized volatility exactly like matrix_QP: sqrt(sum(log_returns^2))
        # First, convert cumulative log returns to minute-by-minute log returns
        minute_log_returns = pair_returns_data.diff().fillna(0.0)  # First diff to get minute returns

        # Calculate realized volatility for each pair (matrix_QP method)
        realized_vol = pd.Series(dtype=float)
        for pair in minute_log_returns.columns:
            log_returns = minute_log_returns[pair].dropna()

            if log_returns.empty:
                realized_vol[pair] = np.nan
                continue

            # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
            sum_sq_returns = (log_returns ** 2).sum()

            if sum_sq_returns > 0:
                realized_vol[pair] = np.sqrt(sum_sq_returns)
            elif sum_sq_returns == 0:
                realized_vol[pair] = 0.0
            else:
                realized_vol[pair] = np.nan

        # Matrix_QP style normalization: divide cumulative returns by realized volatility
        # Replace 0 volatility with NaN to avoid division by zero
        volatility_no_zero = realized_vol.replace(0, np.nan)
        valid_volatility = volatility_no_zero.dropna()

        if not valid_volatility.empty:
            # Align columns - only normalize pairs present in both
            common_pairs = pair_returns_data.columns.intersection(valid_volatility.index)

            if common_pairs.tolist():
                # Select and align data for common pairs
                returns_to_normalize = pair_returns_data[common_pairs]
                volatility_aligned = valid_volatility[common_pairs]

                # Perform normalization using broadcasting (matrix_QP method)
                normalized_pair_returns = returns_to_normalize.div(volatility_aligned, axis=1)

                # Add back columns that couldn't be normalized (as NaN)
                missing_pairs = pair_returns_data.columns.difference(common_pairs)
                if not missing_pairs.empty:
                    for missing_pair in missing_pairs:
                        normalized_pair_returns[missing_pair] = np.nan

                # Ensure original column order is preserved
                normalized_pair_returns = normalized_pair_returns.reindex(columns=pair_returns_data.columns)
            else:
                # Fallback: use raw returns if no common pairs
                normalized_pair_returns = pair_returns_data.copy()
        else:
            # Fallback: use raw returns if no valid volatility
            normalized_pair_returns = pair_returns_data.copy()

        rolling_disp_result = create_rolling_dispersion_chart(normalized_pair_returns)
        plotly_figs['rolling_dispersion'] = rolling_disp_result['figure']
        plotly_figs['near_peak_weights_str'] = rolling_disp_result['near_peak_weights_str']
        plotly_figs['star_weights_str'] = rolling_disp_result['star_weights_str']

    return plotly_figs

def create_plotly_metrics(
    normalized_returns_ts,
    dispersion_ts,
    sentiment_ts,
    area_dispersion_ts,
    hmm_states=None,
    cad_usd_diff_ts=None,
    cusum_pos=None,
    cusum_neg=None,
    cusum_threshold=None,
    cusum_changepoints=None,
    bcpd_changepoints=None,
    dominant_freq_ts=None,
    historical_daily_dispersion_highs=None,
    pair_returns_ts=None,
    historical_pair_std_devs=None,
    dispersion_roc_ts=None,
    roc_background_colors=None,
    basket_returns_ts=None,
    share_xaxes=True
):
    start_time_metrics = timer.time() # Record start time for metrics plotting
    pio.templates.default = "plotly_dark" # Set dark theme for all figures generated
    figs = {} # Dictionary to hold the figures
    base_height = 300 # Define a base height in pixels

    # Define color maps
    basket_colors = {
        "USD": "lime", "EUR": "dodgerblue", "GBP": "red", "AUD": "orange",
        "NZD": "aqua", "CAD": "fuchsia", "CHF": "silver", "JPY": "yellow"
    }
    area_group_colors = AREA_GROUP_COLORS # Use defined AREA_GROUP_COLORS
    default_color = 'white' # Default for missing baskets/groups

    # --- Figure 1: Dispersion / Retracement / HMM / BCPD ---
    if dispersion_ts is not None and not dispersion_ts.empty:
        fig_disp = go.Figure()
        fig_disp.update_layout(
            title='Dispersion / Retracement / HMM / BCPD',
            xaxis_title='Time',
            yaxis=dict(title=dict(text='Std Dev (CSSD)', font=dict(color='magenta')), tickfont=dict(color='magenta')),
            #yaxis2=dict(
            #    title=None, # Hide title
            #    # tickfont=dict(color='orange'), # No need if labels are hidden
            #    showticklabels=False, # Hide tick labels
            #    overlaying='y',
            #    side='right',
            #    showgrid=False,
            #    range=[0, 105] # Start with a fixed range, adjust if needed
            #),
            #    title=None, # Hide title
            #    # tickfont=dict(color='orange'), # No need if labels are hidden
            #    showticklabels=False, # Hide tick labels
            #    overlaying='y',
            #    side='right',
            #    showgrid=False,
            #    range=[0, 105] # Start with a fixed range, adjust if needed
            #),
            #    title=None, # Hide title
            #    # tickfont=dict(color='orange'), # No need if labels are hidden
            #    showticklabels=False, # Hide tick labels
            #    overlaying='y',
            #    side='right',
            #    showgrid=False,
            #    range=[0, 105] # Start with a fixed range, adjust if needed
            #),
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified', # Improved hover mode
            height=base_height * 3 # Set height (Increased)
        )

        # Add Dispersion trace
        fig_disp.add_trace(go.Scatter(
            x=dispersion_ts.index,
            y=dispersion_ts, # Ensure this uses the Series directly
            name='Dispersion (CSSD)',
            line=dict(color='magenta'),
            hovertemplate='Dispersion: %{y:.4f}<extra></extra>', # Basic hover
            yaxis='y1'
        ))

        # --- Set Y-axis range based on current dispersion ---
        if not dispersion_ts.empty:
            current_max_dispersion = dispersion_ts.max() # Ensure this uses the Series directly
            if pd.notna(current_max_dispersion):
                yaxis_upper_range = current_max_dispersion * 1.1
                # Ensure a minimum range if dispersion is very low or zero
                # Ensure a minimum range if dispersion is very low or zero
                # Ensure a minimum range if dispersion is very low or zero
                disp_min = dispersion_ts.min() # Use scalar min directly from the Series
                yaxis_upper_range = max(yaxis_upper_range, disp_min * 1.2 if pd.notna(disp_min) and disp_min > 0 else 0.01) # Avoid zero range
                current_min_dispersion = disp_min # Assign the scalar
                yaxis_lower_range = current_min_dispersion * 0.9 if pd.notna(current_min_dispersion) else 0 # Set lower bound slightly below min
                
                # Create a small gap at the bottom for volume bars if we have ROC data
                if dispersion_roc_ts is not None and roc_background_colors is not None:
                    volume_height = (yaxis_upper_range - yaxis_lower_range) * 0.05
                    yaxis_lower_range = yaxis_lower_range - volume_height
                
                fig_disp.update_layout(yaxis_range=[yaxis_lower_range, yaxis_upper_range])

                # Add volume bars for ROC backgrounds
                if dispersion_roc_ts is not None and roc_background_colors is not None and not dispersion_ts.empty:
                    # Calculate retracement once for use in volume bars
                    running_max = dispersion_ts.cummax()
                    retracement_pct = ((running_max - dispersion_ts) / running_max.replace(0, np.nan) * 100).fillna(0)
                    
                    def process_volume_bars(mask, default_color):
                        if not mask.any():
                            return
                            
                        times = dispersion_roc_ts.index[mask]
                        merged_periods = []
                        current_group = [times[0]]
                        
                        for t in times[1:]:
                            if (t - current_group[-1]).total_seconds() <= 300:  # 5 minutes = 300 seconds
                                current_group.append(t)
                            else:
                                merged_periods.append((current_group[0], current_group[-1]))
                                current_group = [t]
                        
                        if current_group:
                            merged_periods.append((current_group[0], current_group[-1]))
                        
                        # Calculate bar dimensions
                        bar_height = (yaxis_upper_range - yaxis_lower_range) * 0.03  # 3% of range for bar height
                        bar_base = yaxis_lower_range + bar_height * 0.5  # Position bars just above the bottom
                        
                        # Add merged volume bars
                        for start_time, end_time in merged_periods:
                            mid_time = start_time + pd.Timedelta(seconds=(end_time - start_time).total_seconds()/2)
                            
                            # For pink backgrounds, check retracement at midpoint
                            color = default_color
                            if default_color == '#8B0000':  # If it's a pink background period
                                mid_retr = retracement_pct.iloc[retracement_pct.index.get_indexer([mid_time], method='nearest')[0]]
                                if mid_retr > 37.5:  # If retracement is > 37.5%
                                    color = 'black'
                            
                            fig_disp.add_trace(go.Bar(
                                x=[mid_time],  # Center of the bar
                                y=[bar_height],  # Fixed height
                                marker_color=color,
                                opacity=0.5,
                                name='ROC Volume',
                                showlegend=False,
                                base=bar_base,  # Start bars from this y position
                                width=(end_time - start_time).total_seconds() * 1000 + 60000  # Width in milliseconds
                            ))
                    
                    # Process yellow background periods
                    yellow_mask = roc_background_colors == 'yellow'
                    process_volume_bars(yellow_mask, 'white')
                    
                    # Process pink background periods with a darker red color (or black if retracement > 25%)
                    pink_mask = roc_background_colors == 'pink'
                    process_volume_bars(pink_mask, '#8B0000')

        # Add HMM background shapes
        if hmm_states is not None and not hmm_states.dropna().empty:
            unique_states = np.sort(hmm_states.dropna().unique())
            n_unique_states = len(unique_states)
            # Define HMM state colors and labels (adjust as needed)
            state_colors = ['#2E8B57', '#DAA520', '#CD5C5C', '#ADD8E6'][:n_unique_states] # Darker/adjusted HMM colors
            label_map = {0: "Consolidation", 1: "Early Expansion", 2: "Late Expansion", 3: "Peak/Contraction"}
            hmm_state_labels = [label_map.get(int(s), f'State {int(s)}') for s in unique_states]

            hmm_states_aligned = hmm_states.reindex(dispersion_ts.index).ffill().bfill().dropna()
            if not hmm_states_aligned.empty:
                state_changes = hmm_states_aligned.diff().fillna(0) != 0
                change_indices = hmm_states_aligned.index[state_changes]
                # Ensure start and end points are included
                span_indices = pd.DatetimeIndex(
                    [hmm_states_aligned.index[0]] + list(change_indices) + [hmm_states_aligned.index[-1] + pd.Timedelta(minutes=1)] # Add slight offset to end
                ).unique() # Ensure unique indices

                for i in range(len(span_indices) - 1):
                    start_dt, end_dt = span_indices[i], span_indices[i+1]
                    # Get state at the beginning of the span
                    current_state_series = hmm_states_aligned[start_dt:start_dt] # Get state at exact start time
                    if not current_state_series.empty:
                        current_state = current_state_series.iloc[0]
                        if pd.notna(current_state):
                            try:
                                state_idx = np.where(unique_states == current_state)[0][0]
                                color = state_colors[state_idx % len(state_colors)]
                                fig_disp.add_shape(
                                    type="rect",
                                    x0=start_dt, y0=0, x1=end_dt, y1=1, # Use relative y-coords for full span
                                    line=dict(width=0),
                                    fillcolor=color,
                                    opacity=0.3, # Adjust opacity
                                    layer="below",
                                    yref="paper" # Refer to paper for y-coords
                                )
                            except IndexError:
                                print(f"Warning: Could not find state {current_state} in unique_states.")
                            except Exception as e_shape:
                                print(f"Error adding HMM shape for state {current_state}: {e_shape}")

                # Add dummy traces for HMM legend
                for i, state_label in enumerate(hmm_state_labels):
                     fig_disp.add_trace(go.Scatter(
                         x=[None], y=[None], mode='markers',
                         marker=dict(color=state_colors[i % len(state_colors)], size=10, symbol='square'),
                         name=state_label,
                         legendgroup="hmm" # Group HMM legend items
                     ))


        # Add BCPD vertical lines
        if bcpd_changepoints:
            first_bcpd = True
            for cp_ts in bcpd_changepoints:
                 if dispersion_ts.index.min() <= cp_ts <= dispersion_ts.index.max():
                     fig_disp.add_vline(
                         x=cp_ts,
                         line_width=1,
                         line_dash="dash",
                         line_color="yellow",
                         name='BCPD Change' if first_bcpd else '',
                         showlegend=first_bcpd # Show legend only for the first line
                     )
                     first_bcpd = False

        # Add Retracement trace (secondary y-axis)
        try:
            if not pd.api.types.is_datetime64_any_dtype(dispersion_ts.index):
                raise TypeError("Dispersion index must be datetime")
            analysis_tz = dispersion_ts.index.tz or SERVER_TIMEZONE
            start_retracement_dt = analysis_tz.localize(datetime.combine(dispersion_ts.index[0].date(), time(3, 0)))
            dispersion_from_0300 = dispersion_ts[dispersion_ts.index >= start_retracement_dt] # Ensure this uses the Series directly
            
            if not dispersion_ts.empty:
                # Calculate running maximum for retracement calculation
                running_max = dispersion_ts.cummax()
                # Calculate current retracement from historical maximum ((max_so_far - current) / max_so_far) * 100
                retracement_pct = ((running_max - dispersion_ts) / running_max.replace(0, np.nan) * 100).fillna(0)
                retracement_pct[retracement_pct < 0] = 0  # Keep this safety check for dispersion
                
                # --- Add Colored Retracement Segments ---
                # Define colors and thresholds
                retr_colors = ['blue', 'green', 'yellow', 'orange', 'red', 'black']
                retr_thresholds = [0, 6.25, 12.5, 25, 37.5, 50]
                retr_labels = ['0-6.25%', '6.25-12.5%', '12.5-25%', '25-37.5%', '37.5-50%', '50%+']

                # Iterate through thresholds to create segments
                current_segment_x = []
                current_segment_y = []
                current_color_idx = -1

                for i in range(len(retracement_pct)):
                    val = retracement_pct.iloc[i]
                    color_idx = -1
                    if val >= retr_thresholds[5]:
                         color_idx = 5
                    elif val >= retr_thresholds[4]:
                         color_idx = 4
                    elif val >= retr_thresholds[3]:
                         color_idx = 3
                    elif val >= retr_thresholds[2]:
                         color_idx = 2
                    elif val >= retr_thresholds[1]:
                         color_idx = 1
                    elif val >= retr_thresholds[0]:
                         color_idx = 0

                    if i > 0 and color_idx != current_color_idx:
                        # End previous segment and start new one
                        if current_segment_x:
                            # Add the connecting point to the previous segment
                            current_segment_x.append(retracement_pct.index[i])
                            current_segment_y.append(retracement_pct.iloc[i])
                            fig_disp.add_trace(go.Scatter(
                                x=current_segment_x, y=current_segment_y, mode='lines',
                                line=dict(color=retr_colors[current_color_idx], dash='solid'), # Changed to solid
                                yaxis='y2', showlegend=False, # Hide individual segments from legend
                                hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                            ))
                        # Start new segment
                        current_segment_x = [retracement_pct.index[i-1], retracement_pct.index[i]] # Start from previous point
                        current_segment_y = [retracement_pct.iloc[i-1], retracement_pct.iloc[i]]
                        current_color_idx = color_idx
                    else:
                        # Continue current segment
                        if not current_segment_x: # First point
                             current_segment_x.append(retracement_pct.index[i])
                             current_segment_y.append(retracement_pct.iloc[i])
                             current_color_idx = color_idx
                        else:
                             current_segment_x.append(retracement_pct.index[i])
                             current_segment_y.append(retracement_pct.iloc[i])

                # Add the last segment
                if current_segment_x:
                    fig_disp.add_trace(go.Scatter(
                        x=current_segment_x, y=current_segment_y, mode='lines',
                        line=dict(color=retr_colors[current_color_idx], dash='solid'), # Changed to solid
                        yaxis='y2', showlegend=False,
                        hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                    ))

                # Add dummy traces for Retracement legend
                for i, label in enumerate(retr_labels):
                    fig_disp.add_trace(go.Scatter(
                        x=[None], y=[None], mode='lines',
                        line=dict(color=retr_colors[i], dash='solid'), # Changed to solid
                        name=f'Retracement {label}',
                        legendgroup="retracement" # Group legend items
                    ))
                # Update yaxis2 range based on actual data
                max_retracement = retracement_pct.max() if not retracement_pct.empty else 100
                #fig_disp.update_layout(yaxis2_range=[0, max(1, max_retracement * 1.1)]) # Ensure range is at least 1
                fig_disp.update_layout(
                    yaxis2=dict(
                        title=dict(text='Retracement', font=dict(color='yellow')),
                        tickfont=dict(color='yellow'),
                        #showticklabels=True,
                        overlaying='y',
                        side='right',
                        position=0.85,
                        anchor='free',
                        tickvals=[0, 12.5, 25, 37.5, 50, 62.5, 75, 87.5, 100, retracement_pct.max()],
                        ticktext=['0', '12.5', '25', '37.5', '50', '62.5', '75', '87.5', f'{retracement_pct.max():.0f}'],
                        range=[0, retracement_pct.max() * 1.1]
                    ),
                    margin=dict(r=80)  # Add extra right margin for the additional y-axes
                )

                # Add reference lines at specific retracement levels (25%, 37.5%, 50%)
                fig_disp.add_hline(
                    y=12.5, 
                    line=dict(color='green', width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text="12.5%",
                    annotation_position="left",
                    annotation_font=dict(color="green", size=10)
                )

                fig_disp.add_hline(
                    y=25, 
                    line=dict(color='yellow', width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text="25%",
                    annotation_position="left",
                    annotation_font=dict(color="yellow", size=10)
                )

                fig_disp.add_hline(
                    y=37.5, 
                    line=dict(color='orange', width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text="37.5%",
                    annotation_position="left",
                    annotation_font=dict(color="orange", size=10)
                )

                fig_disp.add_hline(
                    y=50, 
                    line=dict(color='red', width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text="50%",
                    annotation_position="left",
                    annotation_font=dict(color="red", size=10)
                )

        except Exception as e_retr:
             print(f"Error during retracement calculation/plotting: {e_retr}")

        # --- NEW: Add Historical Daily High Dispersion Lines ---
        if historical_daily_dispersion_highs is not None and not historical_daily_dispersion_highs.empty:
            print(f"Adding historical dispersion lines (up to 10): {historical_daily_dispersion_highs}") # Debug
            # Generate 10 shades of blue, from lighter to darker
            blue_shades = pc.sample_colorscale('Blues', np.linspace(0.9, 0.2, 10)) # Lighter (0.9) to darker (0.2)
            # Repeat line styles if needed, ensure 10 styles
            line_styles = ['dot', 'dashdot', 'longdash', 'dash', 'solid'] * 2
            # Create labels for up to 10 previous days
            day_labels = [f"Day -{i} High" for i in range(1, 11)]
            day_labels[0] = "Prev Day High" # Adjust first label

            # Ensure highs are sorted oldest to newest for consistent styling application if needed, but labels are fixed
            sorted_highs = historical_daily_dispersion_highs.sort_index(ascending=True)

            # --- Helper function for business days ---
            def count_business_days(start_date, end_date):
                # Ensure we are comparing date objects
                start_date_obj = start_date if isinstance(start_date, pd.Timestamp) else pd.to_datetime(start_date).date()
                end_date_obj = end_date if isinstance(end_date, pd.Timestamp) else pd.to_datetime(end_date).date()
                # np.busday_count counts transitions, so add 1 if start != end and start is a business day
                # It excludes the end date, so we need to adjust.
                # Let's count days between start_date (exclusive) and end_date (inclusive)
                if start_date_obj >= end_date_obj:
                    return 0
                # Count business days strictly between start and end, then add 1 if end_date is a business day
                # This seems overly complex. Let's try a simpler approach:
                # Count business days from start_date to end_date (inclusive range).
                # np.busday_count counts days *between* start and end.
                # Example: Mon to Tue -> 1 busday. np.busday_count(Mon, Wed) = 1 (Tue).
                # Example: Fri to Mon -> 1 busday. np.busday_count(Fri, Tue) = 1 (Mon).
                # We want the number of business days *ago*.
                # If today is Wed and hist is Tue, that's 1 business day ago.
                # If today is Mon and hist is Fri, that's 1 business day ago.
                # np.busday_count(hist_date, today_date) should give the number of business days *between* them.
                return np.busday_count(start_date_obj, end_date_obj)

            # --- Add Annotation Data Calculation ---
            annotation_lines = []
            all_highs_for_annotation = {}
            latest_hist_date_for_labeling = historical_daily_dispersion_highs.index.max() if historical_daily_dispersion_highs is not None and not historical_daily_dispersion_highs.empty else None

            # Add historical highs to the dictionary for sorting
            if historical_daily_dispersion_highs is not None and latest_hist_date_for_labeling is not None:
                 for hist_date, high_value in historical_daily_dispersion_highs.items():
                     if pd.notna(high_value):
                         # Calculate business days ago relative to the latest historical date
                         business_days_ago = count_business_days(hist_date, latest_hist_date_for_labeling)
                         label = f"Day -{business_days_ago}" if business_days_ago > 0 else "Prev Day" # If 0 days between, it's the previous day
                         all_highs_for_annotation[f"{label} High"] = high_value

            # Get current dispersion (last value in the series)
            current_dispersion_value = dispersion_ts.iloc[-1] if not dispersion_ts.empty and pd.notna(dispersion_ts.iloc[-1]) else np.nan # Ensure this uses the Series directly
            # Get today's max dispersion
            todays_max_dispersion_value = dispersion_ts.max() if not dispersion_ts.empty and pd.notna(dispersion_ts.max()) else np.nan # Ensure this uses the Series directly

            # Calculate average of last 10 day highs
            last_10_days_avg = sorted_highs.head(10).mean() if len(sorted_highs) > 0 else np.nan
            
            if pd.notna(todays_max_dispersion_value):
                 all_highs_for_annotation["Today's Max"] = todays_max_dispersion_value
            
            if pd.notna(last_10_days_avg):
                 all_highs_for_annotation["10-Day H Avg"] = last_10_days_avg

            # Sort all highs (historical + today's max) descending by value
            sorted_annotation_highs = sorted(all_highs_for_annotation.items(), key=lambda item: item[1], reverse=True)

            # Format lines for the annotation text with colors
            annotation_lines.append("<b>Dispersion Levels:</b>")
            proximity_threshold = 0.05 # Define the absolute threshold for 'close' values

            # Add Current Dispersion first
            if pd.notna(current_dispersion_value):
                 annotation_lines.append(f"<span style='color:magenta;'>Current: {current_dispersion_value:.4f}</span>") # Use magenta like the line

            for label, value in sorted_annotation_highs:
                 if label == "10-Day H Avg":
                     # For 10-day average, use aqua if current is higher, white otherwise
                     if pd.notna(current_dispersion_value) and pd.notna(value):
                         color = "aqua" if current_dispersion_value > value else "white"
                     else:
                         color = "white"  # Default to white if values are NaN
                 else:
                     color = "white" # Default
                     if pd.notna(current_dispersion_value):
                         difference = value - current_dispersion_value # Signed difference
                         # Check conditions in order: orange, yellow, red, green
                         if difference > 0 and difference <= proximity_threshold: # Above current, but within +0.05 points
                             color = "orange"
                         elif difference < 0 and difference >= -proximity_threshold: # Below current, but within -0.05 points
                             color = "yellow"
                         elif difference > proximity_threshold: # Strictly above current + 0.05
                             color = "red"
                         elif difference < -proximity_threshold: # Strictly below current - 0.05
                             color = "lime" # Use lime green for better visibility on dark background

                 annotation_lines.append(f"<span style='color:{color};'>{label}: {value:.4f}</span>")

            annotation_text = "<br>".join(annotation_lines)
            # --- Add Hlines ---
            num_lines_to_add = min(len(sorted_highs), 10) # Add up to 10 lines
            for i in range(num_lines_to_add):
                hist_date = sorted_highs.index[i]
                high_value = sorted_highs.iloc[i]

                if pd.notna(high_value):
                    # Determine label index based on *business days* ago from the *latest* historical date
                    latest_hist_date = sorted_highs.index[-1]
                    current_hist_date = hist_date
                    # Calculate business days ago using the helper function
                    business_days_ago = count_business_days(current_hist_date, latest_hist_date)
                    label_index = business_days_ago # Use business days for index

                    # Adjust day_labels if needed (should match business days logic)
                    # We might need to dynamically generate labels based on business_days_ago
                    if business_days_ago == 0:
                        label = "Prev Day High"
                    else:
                        label = f"Day -{business_days_ago} High"

                    # Check if label_index is valid for style/color lists (use modulo)
                    if label_index >= 0:
                        label = day_labels[label_index]
                        style = line_styles[label_index % len(line_styles)] # Use modulo for safety
                        color = blue_shades[label_index % len(blue_shades)] # Use modulo for safety

                        fig_disp.add_hline(
                            y=high_value,
                            line_width=1,
                            line_dash=style,
                            line_color=color,
                            annotation_text=f'{label} ({high_value:.4f})', # Increased precision
                            annotation_position="bottom right",
                            annotation_font_size=10,
                            annotation_font_color=color
                        )

            # --- Add Top Right Annotation ---
            fig_disp.add_annotation(
                text=annotation_text,
                align='left',
                showarrow=False,
                xref='paper', # Position relative to the plotting area
                yref='paper',
                x=0.11,       # After first annotation
                y=0.98,       # Slightly below the top edge
                bgcolor="rgba(0,0,0,0.7)", # Semi-transparent black background
                bordercolor="rgba(255,255,255,0.5)", # Light border
                borderwidth=1,
                font=dict(color="white", size=10)
            )
        figs['dispersion'] = fig_disp

    # --- Figure 2: CUSUM ---
    if (cusum_pos is not None and not cusum_pos.empty) or (cusum_neg is not None and not cusum_neg.empty):
        fig_cusum = go.Figure()
        fig_cusum.update_layout(
            title='CUSUM on Dispersion',
            xaxis_title='Time',
            yaxis_title='CUSUM Value',
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified',
            height=base_height # Set height
        )
        if cusum_pos is not None and not cusum_pos.empty:
            fig_cusum.add_trace(go.Scatter(x=cusum_pos.index, y=cusum_pos, name='CUSUM (+)', line=dict(color='cyan'), hovertemplate='CUSUM (+): %{y:.4f}<extra></extra>'))
        if cusum_neg is not None and not cusum_neg.empty:
            # Plotting positive values for negative CUSUM for clarity with threshold
            fig_cusum.add_trace(go.Scatter(x=cusum_neg.index, y=cusum_neg, name='CUSUM (-)', line=dict(color='magenta'), hovertemplate='CUSUM (-): %{y:.4f}<extra></extra>'))
        if cusum_threshold is not None:
            fig_cusum.add_hline(y=cusum_threshold, line_dash="dash", line_color='red', annotation_text=f'Thresh ({cusum_threshold:.2f})', annotation_position="bottom right")

        # Add CUSUM changepoint vertical lines
        if cusum_changepoints:
            first_cusum_cp = True
            
            # Get the index range for filtering from available CUSUM data
            base_index = None
            if cusum_pos is not None and not cusum_pos.empty:
                base_index = cusum_pos.index
            elif cusum_neg is not None and not cusum_neg.empty: # Check cusum_neg if cusum_pos is empty
                base_index = cusum_neg.index

            min_time, max_time = None, None
            if base_index is not None:
                min_time = base_index.min()
                max_time = base_index.max()
                
                for cp_ts in cusum_changepoints:
                    # Check if cp_ts is within the time range
                    if isinstance(cp_ts, (pd.Timestamp, datetime)) and min_time <= cp_ts <= max_time:
                        fig_cusum.add_vline(
                            x=cp_ts,
                            line_width=1,
                            line_dash="dot",
                            line_color="green",
                            name='CUSUM Change' if first_cusum_cp else '',
                            showlegend=first_cusum_cp
                        )
                        first_cusum_cp = False

        figs['cusum'] = fig_cusum

    # --- Figure 3: Spectral Period ---
    if dominant_freq_ts is not None and not dominant_freq_ts.empty:
        fig_spec = go.Figure()
        fig_spec.update_layout(
            title='Dominant Period (Spectral Analysis on Dispersion)',
            xaxis_title='Time',
            yaxis_title='Period (Minutes)',
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified',
            height=base_height # Set height
        )
        dominant_period_ts = 1 / dominant_freq_ts.replace(0, np.nan)
        fig_spec.add_trace(go.Scatter(x=dominant_period_ts.index, y=dominant_period_ts, name='Dominant Period', line=dict(color='teal'), hovertemplate='Period: %{y:.2f} min<extra></extra>'))
        # fig_spec.update_yaxes(range=[0, 120]) # Optional Y limit
        figs['spectral'] = fig_spec

    # --- Figure 4: Sentiment ---
    if sentiment_ts is not None and not sentiment_ts.empty:
        fig_sent = go.Figure()
        fig_sent.update_layout(
            title='Sentiment Metrics',
            xaxis_title='Time',
            yaxis_title='Z-Score Sum / Difference',
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified',
            height=base_height * 2 # Set height (Increased)
        )
        fig_sent.add_trace(go.Scatter(x=sentiment_ts.index, y=sentiment_ts, name='Sentiment (AUD+NZD - CHF+JPY)', line=dict(color='lime'), hovertemplate='Sentiment: %{y:.4f}<extra></extra>'))
        if cad_usd_diff_ts is not None and not cad_usd_diff_ts.empty:
            fig_sent.add_trace(go.Scatter(x=cad_usd_diff_ts.index, y=cad_usd_diff_ts, name='Diff (CAD - USD)', line=dict(color='dodgerblue', dash='dot'), hovertemplate='CAD-USD Diff: %{y:.4f}<extra></extra>'))
        
        # Add divergence trace if available
        if pair_returns_ts is not None and not pair_returns_ts.empty:
            try:
                # Calculate normalized pair returns using matrix_QP realized volatility (same as Rolling Dispersion)
                pair_returns_data = pair_returns_ts  # Cumulative log returns since SOD

                # Calculate realized volatility exactly like matrix_QP: sqrt(sum(log_returns^2))
                # First, convert cumulative log returns to minute-by-minute log returns
                minute_log_returns = pair_returns_data.diff().fillna(0.0)  # First diff to get minute returns

                # Calculate realized volatility for each pair (matrix_QP method)
                realized_vol = pd.Series(dtype=float)
                for pair in minute_log_returns.columns:
                    log_returns = minute_log_returns[pair].dropna()

                    if log_returns.empty:
                        realized_vol[pair] = np.nan
                        continue

                    # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
                    sum_sq_returns = (log_returns ** 2).sum()

                    if sum_sq_returns > 0:
                        realized_vol[pair] = np.sqrt(sum_sq_returns)
                    elif sum_sq_returns == 0:
                        realized_vol[pair] = 0.0
                    else:
                        realized_vol[pair] = np.nan

                # Matrix_QP style normalization: divide cumulative returns by realized volatility
                # Replace 0 volatility with NaN to avoid division by zero
                # Also filter out extremely small volatility values to prevent scaling issues
                min_volatility_threshold = 1e-8  # Minimum volatility threshold
                volatility_no_zero = realized_vol.replace(0, np.nan)
                volatility_filtered = volatility_no_zero.where(volatility_no_zero >= min_volatility_threshold, np.nan)
                valid_volatility = volatility_filtered.dropna()

                normalized_pair_returns = pd.DataFrame(index=pair_returns_ts.index)
                if not valid_volatility.empty:
                    # Align columns - only normalize pairs present in both
                    common_pairs = pair_returns_data.columns.intersection(valid_volatility.index)

                    if common_pairs.tolist():
                        # Select and align data for common pairs
                        returns_to_normalize = pair_returns_data[common_pairs]
                        volatility_aligned = valid_volatility[common_pairs]

                        # Perform normalization using broadcasting (matrix_QP method)
                        normalized_common = returns_to_normalize.div(volatility_aligned, axis=1)

                        # Add normalized pairs to the result DataFrame
                        for pair in common_pairs:
                            normalized_pair_returns[pair] = normalized_common[pair]

                        # Add back columns that couldn't be normalized (as NaN)
                        missing_pairs = pair_returns_data.columns.difference(common_pairs)
                        for missing_pair in missing_pairs:
                            normalized_pair_returns[missing_pair] = np.nan
                    else:
                        # No common pairs - fill with NaN
                        for col in pair_returns_data.columns:
                            normalized_pair_returns[col] = np.nan
                else:
                    # No valid volatility - fill with NaN
                    for col in pair_returns_data.columns:
                        normalized_pair_returns[col] = np.nan
                
                # Calculate top 9 pairs using the same method as the strength chart for consistency
                if not normalized_pair_returns.empty:
                    # Use the same strength ranking logic as the main strength chart
                    ranker = StrengthRanker(rolling_window=60)
                    strength_scores = ranker.calculate_strength_scores(normalized_pair_returns)
                    ranked_pairs_for_divergence = ranker.rank_pairs(strength_scores, top_n=9)

                    # Get the latest top pairs from the strength ranking
                    if not ranked_pairs_for_divergence.empty:
                        latest_ranked = ranked_pairs_for_divergence.iloc[-1].dropna()
                        top_pairs = latest_ranked.index.tolist()
                    else:
                        # Fallback to absolute value method if ranking fails
                        latest_time = normalized_pair_returns.index[-1]
                        latest_abs_returns = normalized_pair_returns.loc[latest_time].abs()
                        top_pairs = latest_abs_returns.nlargest(9).index.tolist()

                    # Filter pair_returns_ts for just these top pairs
                    valid_top_pairs = [p for p in top_pairs if p in pair_returns_ts.columns]
                    
                    if valid_top_pairs:
                        # Calculate the absolute sum of the top 9 pairs (using same pairs as strength chart)
                        top9_returns = pair_returns_ts[valid_top_pairs].abs()
                        top9_abs_sum = top9_returns.sum(axis=1)

                        # Calculate divergence using helper function with matrix_QP normalized returns
                        from calculations import calculate_divergence_top9_vs_basket

                        # Use matrix_QP normalized returns if available, otherwise fallback to historical
                        divergence_normalized_returns = chart_normalized_returns if 'chart_normalized_returns' in locals() and not chart_normalized_returns.empty else normalized_returns_ts

                        abs_returns = divergence_normalized_returns.abs()
                        abs_sum = abs_returns.sum(axis=1)
                        divergence_ts = calculate_divergence_top9_vs_basket(top9_abs_sum, abs_sum)
                        
                        if divergence_ts is not None:
                            # Calculate max range from sentiment and CAD-USD diff
                            max_range = max(
                                sentiment_ts.max() if not sentiment_ts.empty else 0,
                                cad_usd_diff_ts.max() if cad_usd_diff_ts is not None and not cad_usd_diff_ts.empty else 0
                            )
                            min_range = min(
                                sentiment_ts.min() if not sentiment_ts.empty else 0,
                                cad_usd_diff_ts.min() if cad_usd_diff_ts is not None and not cad_usd_diff_ts.empty else 0
                            )
                            
                            # Clip divergence values to stay within range
                            clipped_divergence = divergence_ts.clip(lower=min_range, upper=max_range)
                            
                            # Add trace for divergence without legend
                            fig_sent.add_trace(go.Scatter(
                                x=divergence_ts.index,
                                y=clipped_divergence,
                                mode='lines',
                                line=dict(color='red', width=2),
                                hovertemplate='Divergence: %{y:.4f}<extra></extra>',
                                yaxis='y',  # Use same axis as sentiment
                                showlegend=False  # Hide from legend
                            ))
            except Exception as e:
                print(f"Error calculating divergence for sentiment chart: {e}")
        
        fig_sent.add_hline(y=0, line_dash="dash", line_color='grey')
        figs['sentiment'] = fig_sent

    # --- Figure 5: Normalized Returns (Z-scores) ---
    # Calculate matrix_QP realized volatility for baskets using the proper matrix_QP approach
    matrix_qp_normalized_returns = pd.DataFrame()

    # Check if we have basket returns data to calculate matrix_QP normalization
    if basket_returns_ts is not None and isinstance(basket_returns_ts, pd.DataFrame):
        basket_returns_data = basket_returns_ts  # Cumulative log returns since SOD for baskets

        if not basket_returns_data.empty:
            # Use the matrix_QP approach: calculate realized volatility from minute-by-minute basket returns
            # First, convert cumulative log returns to minute-by-minute log returns
            minute_log_returns_baskets = basket_returns_data.diff().fillna(0.0)  # First diff to get minute returns

            # Calculate realized volatility for each basket using matrix_QP formula
            realized_vol_baskets = pd.Series(dtype=float)
            for basket in minute_log_returns_baskets.columns:
                log_returns = minute_log_returns_baskets[basket].dropna()

                if log_returns.empty:
                    realized_vol_baskets[basket] = np.nan
                    continue

                # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
                sum_sq_returns = (log_returns ** 2).sum()

                # Handle potential non-positive sum before sqrt (same as matrix_QP)
                if sum_sq_returns > 0:
                    realized_vol_baskets[basket] = np.sqrt(sum_sq_returns)
                elif sum_sq_returns == 0:
                    realized_vol_baskets[basket] = 0.0  # Volatility is zero if returns are always zero
                else:
                    # This case shouldn't happen with real numbers
                    realized_vol_baskets[basket] = np.nan  # Assign NaN if sum is negative or NaN

            # Matrix_QP style normalization: divide cumulative returns by realized volatility
            # Replace 0 volatility with NaN to avoid division by zero
            # Also filter out extremely small volatility values to prevent scaling issues
            min_volatility_threshold = 1e-8  # Minimum volatility threshold
            volatility_no_zero = realized_vol_baskets.replace(0, np.nan)
            volatility_filtered = volatility_no_zero.where(volatility_no_zero >= min_volatility_threshold, np.nan)
            valid_volatility_baskets = volatility_filtered.dropna()

            if not valid_volatility_baskets.empty:
                # Align columns - only normalize baskets present in both
                common_baskets = basket_returns_data.columns.intersection(valid_volatility_baskets.index)

                if common_baskets.tolist():
                    # Select and align data for common baskets
                    returns_to_normalize = basket_returns_data[common_baskets]
                    volatility_aligned = valid_volatility_baskets[common_baskets]

                    # Perform normalization using broadcasting (matrix_QP method)
                    normalized_common = returns_to_normalize.div(volatility_aligned, axis=1)

                    # Create the matrix_QP normalized returns DataFrame
                    matrix_qp_normalized_returns = pd.DataFrame(index=basket_returns_data.index)

                    # Add normalized baskets to the result DataFrame
                    for basket in common_baskets:
                        matrix_qp_normalized_returns[basket] = normalized_common[basket]

                    # Add back columns that couldn't be normalized (as NaN)
                    missing_baskets = basket_returns_data.columns.difference(common_baskets)
                    for missing_basket in missing_baskets:
                        matrix_qp_normalized_returns[missing_basket] = np.nan

                    # Ensure original column order is preserved
                    matrix_qp_normalized_returns = matrix_qp_normalized_returns.reindex(columns=basket_returns_data.columns)

    # Use matrix_QP normalized returns if available, otherwise fallback to pre-calculated normalized_returns_ts
    chart_normalized_returns = matrix_qp_normalized_returns if not matrix_qp_normalized_returns.empty else normalized_returns_ts

    if chart_normalized_returns is not None and not chart_normalized_returns.empty:
        fig_norm = go.Figure()

        # Initialize title and convergence fix flag
        chart_title = 'Normalized Basket Log Returns (Realized Vol)'
        convergence_fix_applied = False
        # Check for convergence issue and apply fix if needed
        convergence_threshold = 1e-6  # Threshold to detect convergence

        # Check if all current values are too close to zero (convergence issue)
        if len(chart_normalized_returns) > 10:
            recent_data = chart_normalized_returns.iloc[-10:]  # Last 10 minutes
            recent_abs_max = recent_data.abs().max().max()

            if recent_abs_max < convergence_threshold:
                print(f"Warning: Detected convergence issue in Normalized Basket Log Returns. Recent max absolute value: {recent_abs_max}")

                # Apply fix: Use relative changes from a stable reference point instead of absolute normalized values
                # Find a stable reference point (e.g., 30 minutes ago)
                if len(chart_normalized_returns) > 30:
                    reference_point = chart_normalized_returns.iloc[-30]
                    # Calculate changes from reference point
                    chart_normalized_returns_fixed = chart_normalized_returns.subtract(reference_point, axis=1)

                    # Scale the changes to make them more visible
                    scaling_factor = 1.0 / (chart_normalized_returns_fixed.abs().max().max() + 1e-8)
                    if scaling_factor > 100:  # Only scale if values are very small
                        chart_normalized_returns_fixed = chart_normalized_returns_fixed * min(scaling_factor, 100)

                    chart_normalized_returns = chart_normalized_returns_fixed
                    convergence_fix_applied = True
                    print(f"Applied convergence fix: using relative changes from 30 minutes ago, scaled by {min(scaling_factor, 100):.2f}")

        # Update chart title if convergence fix was applied
        if convergence_fix_applied:
            chart_title += ' (Convergence Fix Applied)'

        # Set up the figure layout
        fig_norm.update_layout(
            title=chart_title,
            xaxis_title='Time',
            yaxis_title='Norm. Log Return (Realized Vol)',
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified',
            height=base_height * 3 # Set height (Increased)
        )

        for col in chart_normalized_returns.columns:
            fig_norm.add_trace(go.Scatter(x=chart_normalized_returns.index, y=chart_normalized_returns[col], name=col, line=dict(color=basket_colors.get(col, default_color)), hovertemplate=f'{col}: %{{y:.4f}}<extra></extra>'))

        # Calculate and add absolute sum and retracement
        try:
            # Take absolute values of all returns
            abs_returns = chart_normalized_returns.abs()

            # For each timestamp, find the maximum absolute value across all baskets
            max_abs_per_time = abs_returns.max(axis=1)

            # Calculate the highest value seen up to each point in time (cumulative max)
            historical_max = max_abs_per_time.cummax()

            # Calculate current retracement from historical maximum with proper handling of edge cases
            # Avoid division by zero and handle initial periods where historical_max might be 0
            retracement_pct = pd.Series(index=historical_max.index, dtype=float)

            # Only calculate retracement where historical_max > 0 to avoid division by zero
            valid_mask = (historical_max > 0) & (historical_max.notna()) & (max_abs_per_time.notna())
            retracement_pct[valid_mask] = ((historical_max[valid_mask] - max_abs_per_time[valid_mask]) / historical_max[valid_mask] * 100)

            # Fill remaining values with 0 (no retracement when no historical max exists)
            retracement_pct = retracement_pct.fillna(0)

            # Ensure retracement is between 0 and 100%
            retracement_pct = retracement_pct.clip(lower=0, upper=100)

            # Calculate absolute sum of normalized returns for baskets
            abs_sum = abs_returns.sum(axis=1)
            
            # Add retracement trace
            fig_norm.add_trace(go.Scatter(
                x=retracement_pct.index,
                y=retracement_pct,
                mode='lines',
                name='Retracement %',
                line=dict(color='white', width=2, dash='dot'),
                hovertemplate='Retracement: %{y:.2f}%<extra></extra>',
                yaxis='y3'  # Use tertiary y-axis for Retracement
            ))

            # Create absolute sum trace
            fig_norm.add_trace(go.Scatter(
                x=abs_sum.index,
                y=abs_sum,
                mode='lines',
                name='Absolute Sum',
                line=dict(color='white', width=3),
                hovertemplate='Absolute Sum: %{y:.2f}<extra></extra>',
                yaxis='y2'  # Use secondary y-axis for Absolute Sum
            ))

            # Add secondary and tertiary y-axes
            max_abs_sum = abs_sum.max() if not abs_sum.empty else 10
            
            fig_norm.update_layout(
                yaxis2=dict(
                    title=dict(text='Absolute Sum', font=dict(color='white')),
                    tickfont=dict(color='white'),
                    overlaying='y',
                    side='right',
                    position=0.85,
                    anchor='free',
                    tickvals=[0, 25, 50, 75, 100, max_abs_sum/2, max_abs_sum],
                    ticktext=['0', '25', '50', '75', '100', f'{max_abs_sum/2:.0f}', f'{max_abs_sum:.0f}'], # Re-added dynamic ticktext
                    range=[0, max_abs_sum * 1.1] # Keep dynamic range for Absolute Sum
                ),
                yaxis3=dict(
                    title=dict(text='Retracement %', font=dict(color='white')),
                    tickfont=dict(color='white'),
                    overlaying='y',
                    side='right',
                    position=0.87, # Position further right
                    anchor='free',
                    tickvals=[0, 25, 50, 75, 100], # Standard percentage ticks
                    ticktext=['0', '25', '50', '75', '100'], # Standard percentage labels
                    range=[0, 100] # Set fixed 0-100 range for Retracement
                ),
                margin=dict(r=120) # Right margin for the axes
            )
        except Exception as e:
            print(f"Error creating retracement/absolute sum lines for normalized returns: {e}")

        figs['normalized_returns'] = fig_norm

    # --- Figure 6: Area Dispersion ---
    if area_dispersion_ts is not None and not area_dispersion_ts.empty:
        fig_area = go.Figure()
        fig_area.update_layout(
            title='Area Dispersion (Std Dev within Groups)',
            xaxis_title='Time',
            yaxis_title='Std Dev',
            legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01), # Legend inside
            hovermode='x unified',
            height=base_height * 3 # Set height (Increased)
        )
        for col in area_dispersion_ts.columns:
            fig_area.add_trace(go.Scatter(x=area_dispersion_ts.index, y=area_dispersion_ts[col], name=col, line=dict(color=area_group_colors.get(col, default_color)), hovertemplate=f'{col}: %{{y:.4f}}<extra></extra>'))
        # fig_area.update_layout(legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)) # Horizontal legend above
        figs['area_dispersion'] = fig_area

    # --- Determine X-axis range ---
    x_range = None
    # Try to get range from a primary timeseries like dispersion or normalized returns
    ts_for_range = None
    if dispersion_ts is not None and not dispersion_ts.empty:
        ts_for_range = dispersion_ts
    elif normalized_returns_ts is not None and not normalized_returns_ts.empty:
        ts_for_range = normalized_returns_ts
    # Add more fallbacks if needed

    if ts_for_range is not None:
        try:
            first_ts = ts_for_range.index[0]
            last_ts = ts_for_range.index[-1]
            # Ensure timezone awareness, fall back to SERVER_TIMEZONE if needed
            tz = first_ts.tz
            if tz is None:
                 print("Warning: Timezone not found in index, using SERVER_TIMEZONE as fallback for range calculation.")
                 tz = SERVER_TIMEZONE # Use the global timezone

            # Calculate start of the day (00:00:00) in the correct timezone
            start_of_day = tz.localize(datetime.combine(first_ts.date(), time.min), is_dst=None) # is_dst=None handles DST transitions

            # Extend range slightly beyond last timestamp to make current minute visible
            x_range = [start_of_day, last_ts + pd.Timedelta(minutes=1)]
            print(f"Calculated x_range (extended): {x_range}") # Debug print
        except Exception as e_range:
            print(f"Error calculating x-axis range: {e_range}")
            x_range = None # Reset range if error occurs

    # --- Apply common layout properties and X-axis range ---
    first_fig_key = next(iter(figs), None)
    if first_fig_key and share_xaxes:
        # Apply range and margins to all figures
        for key, fig in figs.items():
            fig.update_layout(margin=dict(l=40, r=20, t=50, b=40)) # Adjusted margins
            if x_range:
                fig.update_xaxes(range=x_range) # Set the calculated range
            if key != first_fig_key:
                 fig.update_xaxes(matches='x') # Match x-axis with the first figure (redundant if range is set on all, but safe)
    else: # Apply margins and range even if not sharing axes
         for fig in figs.values():
            fig.update_layout(margin=dict(l=40, r=20, t=50, b=40))
            if x_range:
                fig.update_xaxes(range=x_range) # Set the calculated range

    # --- Strength Chart ---
    try:
        # Normalize pair returns using matrix_QP realized volatility (same as Rolling Dispersion and Sentiment)
        normalized_pair_returns = pd.DataFrame(index=pair_returns_ts.index)
        if pair_returns_ts is not None and not pair_returns_ts.empty:
            # Calculate realized volatility exactly like matrix_QP: sqrt(sum(log_returns^2))
            # First, convert cumulative log returns to minute-by-minute log returns
            minute_log_returns = pair_returns_ts.diff().fillna(0.0)  # First diff to get minute returns

            # Calculate realized volatility for each pair (matrix_QP method)
            realized_vol = pd.Series(dtype=float)
            for pair in minute_log_returns.columns:
                log_returns = minute_log_returns[pair].dropna()

                if log_returns.empty:
                    realized_vol[pair] = np.nan
                    continue

                # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
                sum_sq_returns = (log_returns ** 2).sum()

                if sum_sq_returns > 0:
                    realized_vol[pair] = np.sqrt(sum_sq_returns)
                elif sum_sq_returns == 0:
                    realized_vol[pair] = 0.0
                else:
                    realized_vol[pair] = np.nan

            # Matrix_QP style normalization: divide cumulative returns by realized volatility
            # Replace 0 volatility with NaN to avoid division by zero
            # Also filter out extremely small volatility values to prevent scaling issues
            min_volatility_threshold = 1e-8  # Minimum volatility threshold
            volatility_no_zero = realized_vol.replace(0, np.nan)
            volatility_filtered = volatility_no_zero.where(volatility_no_zero >= min_volatility_threshold, np.nan)
            valid_volatility = volatility_filtered.dropna()

            if not valid_volatility.empty:
                # Align columns - only normalize pairs present in both
                common_pairs = pair_returns_ts.columns.intersection(valid_volatility.index)

                if common_pairs.tolist():
                    # Select and align data for common pairs
                    returns_to_normalize = pair_returns_ts[common_pairs]
                    volatility_aligned = valid_volatility[common_pairs]

                    # Perform normalization using broadcasting (matrix_QP method)
                    normalized_common = returns_to_normalize.div(volatility_aligned, axis=1)

                    # Add normalized pairs to the result DataFrame
                    for pair in common_pairs:
                        normalized_pair_returns[pair] = normalized_common[pair]

                    # Add back columns that couldn't be normalized (as NaN)
                    missing_pairs = pair_returns_ts.columns.difference(common_pairs)
                    for missing_pair in missing_pairs:
                        normalized_pair_returns[missing_pair] = np.nan
                else:
                    # No common pairs - fill with NaN
                    for col in pair_returns_ts.columns:
                        normalized_pair_returns[col] = np.nan
            else:
                # No valid volatility - fill with NaN
                for col in pair_returns_ts.columns:
                    normalized_pair_returns[col] = np.nan

        # Compute strength scores and rankings on pairs
        ranker = StrengthRanker(rolling_window=60)
        strength_scores = ranker.calculate_strength_scores(normalized_pair_returns)
        ranked_pairs = ranker.rank_pairs(strength_scores, top_n=9)

        # Build strength chart
        strength_chart_builder = StrengthChartBuilder(top_n=9)
        strength_chart_fig = strength_chart_builder.build_chart(ranked_pairs)

        # Add the separate table chart
        strength_table_fig = strength_chart_builder.build_table_chart(ranked_pairs)

        # Align x-axis with other charts
        try:
            if 'x_range' in locals() and x_range is not None:
                strength_chart_fig.update_xaxes(range=x_range)
        except Exception:
            pass

        figs['strength_chart'] = strength_chart_fig
        figs['strength_table'] = strength_table_fig  # Add table as a separate figure

        # --- Compute normalized weights string for top 9 pairs ---
        latest_strengths = ranked_pairs.iloc[-1].dropna()
        if not latest_strengths.empty:
            abs_sum = np.sum(np.abs(latest_strengths.values))
            if abs_sum > 0:
                normalized_weights = latest_strengths / abs_sum
                # Sort by normalized weight descending (including negatives)
                sorted_pairs = normalized_weights.sort_values(ascending=False)
                weights_str = ",".join([f"{pair}:{weight:.4f}" for pair, weight in sorted_pairs.items()])
            else:
                weights_str = ""
        else:
            weights_str = ""
        figs['top9_weights_str'] = weights_str
    except Exception as e:
        print(f"Error generating strength chart: {e}")

    # --- Custom Strongest vs Weakest Basket Pair Charts ---
    # Check if pair_returns_ts is available and not empty before proceeding
    if pair_returns_ts is None or pair_returns_ts.empty:
        print("Skipping strongest vs weakest charts: Pair returns are empty.")
    else:
        try:
            # Calculate matrix_QP realized volatility normalization for strongest vs weakest charts
            # First, convert cumulative log returns to minute-by-minute log returns
            minute_log_returns = pair_returns_ts.diff().fillna(0.0)  # First diff to get minute returns

            # Calculate realized volatility for each pair (matrix_QP method)
            realized_vol = pd.Series(dtype=float)
            for pair in minute_log_returns.columns:
                log_returns = minute_log_returns[pair].dropna()

                if log_returns.empty:
                    realized_vol[pair] = np.nan
                    continue

                # Matrix_QP formula: Realized volatility = sqrt(sum(log_returns^2))
                sum_sq_returns = (log_returns ** 2).sum()

                if sum_sq_returns > 0:
                    realized_vol[pair] = np.sqrt(sum_sq_returns)
                elif sum_sq_returns == 0:
                    realized_vol[pair] = 0.0
                else:
                    realized_vol[pair] = np.nan

            # Matrix_QP style normalization: divide cumulative returns by realized volatility
            # Replace 0 volatility with NaN to avoid division by zero
            # Also filter out extremely small volatility values to prevent scaling issues
            min_volatility_threshold = 1e-8  # Minimum volatility threshold
            volatility_no_zero = realized_vol.replace(0, np.nan)
            volatility_filtered = volatility_no_zero.where(volatility_no_zero >= min_volatility_threshold, np.nan)
            valid_volatility = volatility_filtered.dropna()

            # Create normalized returns for strongest vs weakest analysis
            normalized_pair_returns_svw = pd.DataFrame(index=pair_returns_ts.index)
            if not valid_volatility.empty:
                # Align columns - only normalize pairs present in both
                common_pairs = pair_returns_ts.columns.intersection(valid_volatility.index)

                if common_pairs.tolist():
                    # Select and align data for common pairs
                    returns_to_normalize = pair_returns_ts[common_pairs]
                    volatility_aligned = valid_volatility[common_pairs]

                    # Perform normalization using broadcasting (matrix_QP method)
                    normalized_common = returns_to_normalize.div(volatility_aligned, axis=1)

                    # Add normalized pairs to the result DataFrame
                    for pair in common_pairs:
                        normalized_pair_returns_svw[pair] = normalized_common[pair]

                    # Add back columns that couldn't be normalized (as NaN)
                    missing_pairs = pair_returns_ts.columns.difference(common_pairs)
                    for missing_pair in missing_pairs:
                        normalized_pair_returns_svw[missing_pair] = np.nan
                else:
                    # No common pairs - fill with NaN
                    for col in pair_returns_ts.columns:
                        normalized_pair_returns_svw[col] = np.nan
            else:
                # No valid volatility - fill with NaN
                for col in pair_returns_ts.columns:
                    normalized_pair_returns_svw[col] = np.nan

            # Use the matrix_QP normalized returns for strongest vs weakest analysis
            if not normalized_pair_returns_svw.empty:
                latest_time = normalized_pair_returns_svw.index[-1]
                latest_strengths = normalized_pair_returns_svw.loc[latest_time].sort_values(ascending=False)
            else:
                # Fallback to using normalized_returns_ts if matrix_QP calculation fails
                latest_time = normalized_returns_ts.index[-1]
                latest_strengths = normalized_returns_ts.loc[latest_time].sort_values(ascending=False)

            # Define groups: (top N strongest, bottom M weakest)
            groups = [
                (1, 4),
                (2, 3),
                (3, 2)
            ]

            for idx, (n_strong, n_weak) in enumerate(groups, 1):
                strongest_baskets = latest_strengths.head(n_strong).index.tolist()
                weakest_baskets = latest_strengths.tail(n_weak).index.tolist()

                pair_symbols = []
                pair_labels = []
                pair_series = {}

                for strong in strongest_baskets:
                    for weak in weakest_baskets:
                        # Compose symbol
                        direct = strong + weak
                        inverse = weak + strong

                        symbol = None
                        invert = False
                        label = f"{strong}{weak}"

                        if direct in ALL_SYMBOLS:
                            symbol = direct
                        elif inverse in ALL_SYMBOLS:
                            symbol = inverse
                            invert = True
                            label = f"-{inverse}"
                        else:
                            continue  # Skip if neither exists

                        # Fetch returns
                        if symbol in pair_returns_ts.columns:
                            returns = pair_returns_ts[symbol].copy()
                            if invert:
                                # Invert cumulative returns (handle potential division by zero if return is -1)
                                denominator = (1 + returns)
                                returns = np.where(denominator != 0, -returns / denominator, np.nan) # Use np.where for safety

                            # Normalize using matrix_QP realized volatility
                            if symbol in valid_volatility.index:
                                volatility = valid_volatility[symbol]
                                if pd.notna(volatility) and volatility > 1e-9:
                                    norm_returns = returns / volatility
                                else:
                                    norm_returns = pd.Series(np.nan, index=returns.index)
                            else:
                                norm_returns = pd.Series(np.nan, index=returns.index)

                            pair_symbols.append(symbol)
                            pair_labels.append(label)
                            pair_series[label] = norm_returns

                # Build DataFrame
                if pair_series:
                    df = pd.DataFrame(pair_series)

                    # Plot only if DataFrame is not empty
                    if not df.empty:
                        fig = go.Figure()
                        fig.update_layout(
                            title=f"Top {n_strong} Strongest vs {n_weak} Weakest Baskets",
                            xaxis_title='Time',
                            yaxis_title='Norm. Log Return (Realized Vol)',
                            hovermode='x unified',
                            height=600,
                            template='plotly_dark'
                        )

                        # Color scale (handle potential empty df or all NaNs)
                        min_score = df.min(skipna=True).min(skipna=True)
                        max_score = df.max(skipna=True).max(skipna=True)

                        # Ensure min/max are valid numbers before creating gradient
                        if pd.notna(min_score) and pd.notna(max_score) and not df.columns.empty:
                            gradient = ColorGradient(n_colors=len(df.columns))

                            for col in df.columns:
                                # Ensure iloc[-1] is safe for potentially single-row or all-NaN columns
                                last_val = df[col].dropna().iloc[-1] if not df[col].dropna().empty else np.nan
                                color = gradient.score_to_color(last_val, min_score, max_score) if pd.notna(last_val) else 'grey' # Default color if last_val is NaN

                                fig.add_trace(go.Scatter(
                                    x=df.index,
                                    y=df[col],
                                    mode='lines',
                                    name=col,
                                    line=dict(color=color, width=2),
                                    hovertemplate=f'{col}: %{{y:.4f}}<extra></extra>'
                                ))
                        else:
                             print(f"Warning: Cannot generate color gradient for Strong/Weak chart {idx} due to empty data or NaNs.")
                             # Optionally add a message to the chart itself

                        figs[f'strength_vs_weakest_{idx}'] = fig
                    else:
                         print(f"Skipping plot for Strong/Weak chart {idx}: No valid pair data.")

        except IndexError as ie:
             # Catch specific error if latest_time access fails even with the initial check (shouldn't happen now)
             print(f"Error accessing latest data for strongest vs weakest charts (IndexError): {ie}")
        except Exception as e:
            # General exception handler for other potential errors in this block
            print(f"Error generating strongest vs weakest basket charts: {e}")

    end_time_metrics = timer.time() # Record end time for metrics plotting
    duration_metrics = end_time_metrics - start_time_metrics
    print(f"Time to prepare and plot all metrics charts: {duration_metrics:.4f} seconds")

    return figs

class ChartUpdateService:
    """
    Watches for new data and updates the strength chart.
    """
    def __init__(self, data_fetch_callback, chart_builder: StrengthChartBuilder):
        """
        Args:
            data_fetch_callback: Callable returning ranked returns DataFrame
            chart_builder: Instance of StrengthChartBuilder
        """
        self.data_fetch_callback = data_fetch_callback
        self.chart_builder = chart_builder
        self.current_figure = None

    def update(self):
        """
        Fetch new data and update the chart.
        """
        try:
            ranked_returns = self.data_fetch_callback()
            self.current_figure = self.chart_builder.build_chart(ranked_returns)
            return self.current_figure
        except Exception as e:
            print(f"ChartUpdateService: Error updating chart: {e}")
            return None